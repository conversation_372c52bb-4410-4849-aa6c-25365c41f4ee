/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.application.util;

import com.lowagie.text.pdf.BarcodeEAN;

import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.imageio.ImageIO;

import net.glxn.qrgen.QRCode;

/**
 * <AUTHOR>
 */
public class RadicadoImage {

    private Date fechaCreacion;

    public String Generar(Date fecha, String nombreEntidad, String numeroRadicado, String logoPath, String destinationPath, String Usuario, String Destinatario, Boolean logoOption) {
        fechaCreacion = fecha;
        return Generar(nombreEntidad, numeroRadicado, logoPath, destinationPath, Usuario, Destinatario, logoOption, null, null);
    }

    public String Generar(Date fecha, String nombreEntidad, String numeroRadicado, String logoPath, String destinationPath, String usuario, String destinatario, Boolean logoOption, String serie, String subserie) {
        fechaCreacion = fecha;
        return Generar(nombreEntidad, numeroRadicado, logoPath, destinationPath, usuario, destinatario, logoOption, serie, subserie);
    }


    public String Generar(String nombreEntidad, String numeroRadicado, String logoPath, String destinationPath, String usuario, String destinatario, Boolean logoOption, String serie, String subserie) {
        try {
            File path = new File(logoPath);
            BufferedImage logo = null;
            if (destinatario.isEmpty()) {
                if (logoOption == true) {
                    logo = ImageIO.read(new File(path, "logoInstitucion.png"));
                }
            } else {
                logo = ImageIO.read(new File(path, "logoInstitucion.png"));
            }

            BufferedImage qrCode = ImageIO.read(GenerarQRCode(numeroRadicado, 110, 110));
            BufferedImage img = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);

            int Ratio = 3;
            Graphics2D g2D = img.createGraphics();

            Font font = new Font("Monospaced", Font.BOLD, 10);
            g2D.setFont(font);
            FontMetrics fm = g2D.getFontMetrics();

            ArrayList<String> sizes = new ArrayList<String>() {
                {
                    add(numeroRadicado);

                    add(nombreEntidad);

                    add(destinatario);

                    add(usuario);
                }
            };

            int width;
            String criteria = max(sizes);
            if ("------------------------------".length() > criteria.length()) {
                if (destinatario.isEmpty()) {
                    width = fm.stringWidth("------------------------------") + (logo != null ? logo.getWidth() : 110) / Ratio + 130;
                } else {
                    width = fm.stringWidth("------------------------------") + (logo.getWidth() / Ratio) + 130;
                }
            } else {
                if (destinatario.isEmpty()) {
                    width = fm.stringWidth(criteria) + (logo != null ? logo.getWidth() : 110) / Ratio + 130;
                } else {
                    width = fm.stringWidth(criteria) + (logo.getWidth() / Ratio) + 130;
                }
            }

            if (destinatario.isEmpty()) {
                g2D.drawString("", (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent());
            } else {
                g2D.drawString("", (logo.getWidth() / Ratio), fm.getAscent());
            }

            int height;
            if (destinatario.isEmpty()) {
                height = Math.max(fm.getHeight() * 8, (logo != null ? logo.getHeight() : 297) / Ratio);
            } else {
                height = Math.max(fm.getHeight() * 8, logo.getHeight() / Ratio);
            }
            g2D.dispose();

            img = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            g2D = img.createGraphics();
            g2D.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
            g2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2D.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            g2D.setRenderingHint(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_ENABLE);
            g2D.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
            g2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2D.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2D.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
            g2D.setFont(font);
            fm = g2D.getFontMetrics();
            g2D.setColor(Color.black);
            g2D.drawImage(logo, 0, 5, 94, 94, null);
            g2D.drawImage(qrCode, width - qrCode.getWidth(), -8, 120, 120, null);

            if (destinatario.isEmpty()) {
                g2D.drawString(nombreEntidad, (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * 2);
            } else {
                g2D.drawString(nombreEntidad, (logo.getWidth() / Ratio), fm.getAscent() * 2);
            }
            font = new Font("Monospaced", Font.BOLD, 12);
            g2D.setFont(font);
            if (destinatario.isEmpty()) {
                g2D.drawString(numeroRadicado, (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * 3);
            } else {
                g2D.drawString(numeroRadicado, (logo.getWidth() / Ratio), fm.getAscent() * 3);
            }
            font = new Font("Monospaced", Font.BOLD, 10);
            g2D.setFont(font);

            if (destinatario.isEmpty()) {
                g2D.drawString("GESTIONADO POR --------------", (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * 4);
                g2D.drawString(usuario, (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * 5);
            } else {
                g2D.drawString("SE: " + serie + " - SUB: " + subserie, (logo.getWidth() / Ratio), fm.getAscent() * 4);
                g2D.drawString(usuario, (logo.getWidth() / Ratio), fm.getAscent() * 5);
            }

            int line;
            if (!destinatario.isEmpty()) {
                g2D.drawString("DESTINATARIO ----------------", (logo.getWidth() / Ratio), fm.getAscent() * 6);
                g2D.drawString(destinatario, (logo.getWidth() / Ratio), fm.getAscent() * 7);
                line = 8;
            } else {
                g2D.drawString("-----------------------------", (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * 6);
                g2D.drawString("-----------------------------", (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * 7);
                line = 8;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date today;
            if (this.fechaCreacion != null) {
                today = this.fechaCreacion;
            } else {
                today = new Date();
            }
            g2D.setColor(Color.black);
            if (destinatario.isEmpty()) {
                g2D.drawString(sdf.format(today), (logo != null ? logo.getWidth() : 110) / Ratio, fm.getAscent() * line);
            } else {
                g2D.drawString(sdf.format(today), (logo.getWidth() / Ratio), fm.getAscent() * line);
            }
            g2D.dispose();

            ImagesUtil util = new ImagesUtil(img);
            util.makeBackgroundTransparent(new File(destinationPath + numeroRadicado + ".png"));
        } catch (IOException e) {
            Logger.getLogger(RadicadoImage.class.getName()).log(Level.SEVERE, e.getMessage());
        }
        return numeroRadicado + ".png";
    }

    public Image GenerarCodeBar(String texto) {
        BarcodeEAN codeEAN;
        codeEAN = new BarcodeEAN();
        codeEAN.setCodeType(codeEAN.EAN13);
        codeEAN.setCode(texto);
        Image imageEAN = codeEAN.createAwtImage(Color.BLACK, Color.WHITE);
        return imageEAN;
    }

    public File GenerarQRCode(String data, int width, int height) {
        return QRCode.from(data).withSize(width, height).file();
    }

    /**
     * Retorna la cadena de texto de mayor longitud pasada como parámetro.
     * @param t
     * @return
     */
    private String max(ArrayList<String> t) {
        String maximum = t.get(0);   // start with the first value
        for (int i = 1; i < t.size(); i++) {
            if (t.get(i).length() > maximum.length()) {
                maximum = t.get(i);   // new maximum
            }
        }
        return maximum;
    }

}
