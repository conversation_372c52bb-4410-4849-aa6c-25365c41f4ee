/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.application;

import com.base16.gedsys.domain.model.entityListeners.ConfiguracionListener;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

/**
 * <AUTHOR>
 */
@Entity
@EntityListeners(ConfiguracionListener.class)
@Table(name = "configuracion", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
        @NamedQuery(name = "Configuracion.findAll", query = "SELECT c FROM Configuracion c")
        , @NamedQuery(name = "Configuracion.findById", query = "SELECT c FROM Configuracion c WHERE c.id = :id")
        , @NamedQuery(name = "Configuracion.findByCreadoPor", query = "SELECT c FROM Configuracion c WHERE c.creadoPor = :creadoPor")
        , @NamedQuery(name = "Configuracion.findByFechaCreacion", query = "SELECT c FROM Configuracion c WHERE c.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "Configuracion.findByFechaModificacion", query = "SELECT c FROM Configuracion c WHERE c.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "Configuracion.findByModificadoPor", query = "SELECT c FROM Configuracion c WHERE c.modificadoPor = :modificadoPor")
        , @NamedQuery(name = "Configuracion.findByType", query = "SELECT c FROM Configuracion c WHERE c.tipoConfiguracion = :type")
        , @NamedQuery(name = "Configuracion.findByNombre", query = "SELECT c FROM Configuracion c WHERE c.nombre = :nombre")})
public class Configuracion implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @Column(name = "deleted")
    private Boolean deleted;
    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "creado_por", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @Column(name = "fecha_creacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "fecha_modificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "modificado_por", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @Column(name = "nombre")
    private String nombre;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "valor")
    private String valor;
    @Column(name = "tipo_configuracion")
    private String tipoConfiguracion;


    public Configuracion() {
    }

    public Configuracion(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getTipoConfiguracion() {
        return tipoConfiguracion;
    }

    public void setTipoConfiguracion(String tipoConfiguracion) {
        this.tipoConfiguracion = tipoConfiguracion;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Configuracion)) {
            return false;
        }
        Configuracion other = (Configuracion) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.Configuracion[ id=" + id + " ]";
    }

}
