package com.base16.gedsys.Enumeration;

public enum TipoDocumentoPorEnviarEnum {
    COMUNICACION_OFICIAL("comunicacionOficial"),

    RESPUESTA_PQRSF("respuestaPQRSF");

    private String tipo;

    TipoDocumentoPorEnviarEnum(String tipo){this.tipo = tipo;}

    public String getTipo() {
        return tipo;
    }

    public static TipoDocumentoPorEnviarEnum traducirTipoDocumentoPorEnviar(String tipoDocumento){
        for (TipoDocumentoPorEnviarEnum tipoDocumentoPorEnviar :
                TipoDocumentoPorEnviarEnum.values()) {
            if (tipoDocumentoPorEnviar.getTipo().equals(tipoDocumento)) return tipoDocumentoPorEnviar;
        }
        return null;
    }
}