package com.base16.gedsys.Enumeration;

public enum EstadoIncidenciaEspecificaEnum {
    FELICITACION(0),

    PENDIENTE(1),

    RESPONDIDA(2),

    SHARED_WITH_ME(3),

    DENUNCIA(4);

    private Integer estado;

    EstadoIncidenciaEspecificaEnum(Integer estado){this.estado = estado;}

    public Integer getEstado() {
        return estado;
    }

    public static EstadoIncidenciaEspecificaEnum traducirEstado(Integer estado){
        for (EstadoIncidenciaEspecificaEnum estadoEspecifica :
                EstadoIncidenciaEspecificaEnum.values()) {
            if (estadoEspecifica.getEstado().equals(estado)) return estadoEspecifica;
        }
        return null;
    }

}
