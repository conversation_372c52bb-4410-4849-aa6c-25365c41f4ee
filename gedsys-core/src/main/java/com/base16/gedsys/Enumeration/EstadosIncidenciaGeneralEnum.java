package com.base16.gedsys.Enumeration;

public enum EstadosIncidenciaGeneralEnum {
    /**
     * Cuando se recibe vía web, pero requiere ser completada por un funcionario para ser enviada a descargos.
     */
    RECIBIDA(0),

    /**
     * Pendiente de la respuesta de los implicados para construir la respuesta final
     */
    DESCARGOS(1),

    /**
     * Se está redactando la respuesta para el destinatario
     */
    PENDIENTE_RESPUESTA(2),

    /**
     * Se firmó el borrador con la respuesta a la incidencia y está pendiente de su envío
     */
    RESPONDIDA(3),

    /**
     * La carta con la respuesta fué enviada y está pendiente por archivar.
     */
    FINALIZADA(4),

    /**
     * El funcionario encargado de la recepción considera que no es una solicitud válida.
     */
    DESCARTADA(5);


    private Integer estadoIncidencia;

    EstadosIncidenciaGeneralEnum(Integer estado) {
        estadoIncidencia = estado;
    }

    public Integer getEstado() {
        return estadoIncidencia;
    }

    /**
     * Permite obtener el enum del estado de la incidencia
     * a partir del valor de dicho estado.
     * @param estado Valor entero.
     * @return Enum del estado.
     */
    public static EstadosIncidenciaGeneralEnum traducirEstado(Integer estado){
        for (EstadosIncidenciaGeneralEnum estadoIncidencia :
                EstadosIncidenciaGeneralEnum.values()) {
            if (estadoIncidencia.getEstado().equals(estado)) return estadoIncidencia;
        }
        return null;
    }
}
