/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.archive.Serie;
import com.base16.gedsys.domain.model.archive.SubSerie;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.codehaus.jackson.annotate.JsonIgnore;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "circular", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Circular.findAll", query = "SELECT c FROM Circular c")
    , @NamedQuery(name = "Circular.findById", query = "SELECT c FROM Circular c WHERE c.id = :id")
    , @NamedQuery(name = "Circular.findByDocumentoFirmado", query = "SELECT c FROM Circular c WHERE c.documentoFirmado = :documentoFirmado")
    , @NamedQuery(name = "Circular.findByGrupoDestinatario", query = "SELECT c FROM Circular c WHERE c.grupoDestinatario = :grupoDestinatario")
    , @NamedQuery(name = "Circular.findByAsunto", query = "SELECT c FROM Circular c WHERE c.asunto = :asunto")
    , @NamedQuery(name = "Circular.findByFechaCreacion", query = "SELECT c FROM Circular c WHERE c.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Circular.findByFechaModifiacion", query = "SELECT c FROM Circular c WHERE c.fechaModifiacion = :fechaModifiacion")
    , @NamedQuery(name = "Circular.findByFechaFirma", query = "SELECT c FROM Circular c WHERE c.fechaFirma = :fechaFirma")
    , @NamedQuery(name = "Circular.findByEstadoYUsuario", query = "SELECT c FROM Circular c WHERE c.estado = :estado AND (c.remitente = :usuario OR c.creadoPor = :creadoPor)")
    , @NamedQuery(name = "Circular.findByEstado", query = "SELECT c FROM Circular c WHERE c.estado = :estado")})

public class Circular implements Serializable {

    @Column(name = "active")
    private Boolean active;

    @Size(max = 50)
    @Column(name = "numero")
    private String numero;

    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;
    @OneToMany(mappedBy = "circular")
    private List<CircularRemInterno> circularRemInternoList;

    @Column(name = "isExtern")
    private Boolean isExtern;

    @Column(name = "fechaDocumento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaDocumento;

    @JoinColumn(name = "ciudad", referencedColumnName = "Id")
    @ManyToOne
    private Municipio ciudad;

    @Size(max = 255)
    @Column(name = "CargoFirmaUno")
    private String cargoFirmaUno;

    @Size(max = 255)
    @Column(name = "CargoFirmaDos")
    private String cargoFirmaDos;

    @Lob
    @Column(name = "comentario")
    private String comentario;

    @JoinColumn(name = "quienProyecto", referencedColumnName = "Id")
    @ManyToOne
    private Usuario quienProyecto;

    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;

    @JoinColumn(name = "SeccionSubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccionSubSeccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;

    @Column(name = "Despedida")
    private String despedida;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Column(name = "GrupoDestinatario")
    private String grupoDestinatario;
    @Column(name = "Asunto")
    private String asunto;
    @Lob
    @Column(name = "Contenido")
    private String contenido;
    @Lob
    @Column(name = "Anexos")
    private String anexos;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModifiacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModifiacion;
    @Column(name = "FechaFirma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;
    @Column(name = "FechaFirmaRemitente1")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaRemitente1;
    @Column(name = "FechaFirmaRemitente2")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaRemitente2;
    @Size(max = 50)
    @Column(name = "Estado")
    private String estado;

    @Column(name = "TipoComunicacion")
    private String tipoComunicacion;

    @JoinColumn(name = "Remitente", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario remitente;
    @JoinColumn(name = "Remitente2", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario remitente2;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario modificadoPor;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "circular")
    private List<CircularConCopia> circularccList;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "ConCopiaExterna")
    private String conCopiaExterna;

    @JoinColumn(name = "DocumentoFirmado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoFirmado;

    public Circular() {
    }

    public Circular(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    public String getGrupoDestinatario() {
        return grupoDestinatario;
    }

    public void setGrupoDestinatario(String grupoDestinatario) {
        this.grupoDestinatario = grupoDestinatario;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getContenido() {
        return contenido;
    }

    public void setContenido(String contenido) {
        this.contenido = contenido;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModifiacion;
    }

    public void setFechaModifiacion(Date fechaModifiacion) {
        this.fechaModifiacion = fechaModifiacion;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public Date getFechaFirmaRemitente1() {
        return fechaFirmaRemitente1;
    }

    public void setFechaFirmaRemitente1(Date fechaFirmaRemitente1) {
        this.fechaFirmaRemitente1 = fechaFirmaRemitente1;
    }

    public Date getFechaFirmaRemitente2() {
        return fechaFirmaRemitente2;
    }

    public void setFechaFirmaRemitente2(Date fechaFirmaRemitente2) {
        this.fechaFirmaRemitente2 = fechaFirmaRemitente2;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public Usuario getRemitente() {
        return remitente;
    }

    public void setRemitente(Usuario remitente) {
        this.remitente = remitente;
    }

    public Usuario getRemitente2() {
        return remitente2;
    }

    public void setRemitente2(Usuario remitente2) {
        this.remitente2 = remitente2;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public String getTipoComunicacion() {
        return tipoComunicacion;
    }

    public void setTipoComunicacion(String tipoComunicacion) {
        this.tipoComunicacion = tipoComunicacion;
    }

    @XmlTransient
    @JsonIgnore
    public List<CircularConCopia> getCircularccList() {
        return circularccList;
    }

    public void setCircularccList(List<CircularConCopia> circularccList) {
        this.circularccList = circularccList;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Circular)) {
            return false;
        }
        Circular other = (Circular) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Circular[ id=" + id + " ]";
    }

    public String getDespedida() {
        return despedida;
    }

    public void setDespedida(String despedida) {
        this.despedida = despedida;
    }

    public SeccionSubSeccion getSeccionSubSeccion() {
        return seccionSubSeccion;
    }

    public void setSeccionSubSeccion(SeccionSubSeccion seccionSubSeccion) {
        this.seccionSubSeccion = seccionSubSeccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    public Usuario getQuienProyecto() {
        return quienProyecto;
    }

    public void setQuienProyecto(Usuario quienProyecto) {
        this.quienProyecto = quienProyecto;
    }

    public String getConCopiaExterna() {
        return conCopiaExterna;
    }

    public void setConCopiaExterna(String conCopiaExterna) {
        this.conCopiaExterna = conCopiaExterna;
    }

    public String getCargoFirmaUno() {
        return cargoFirmaUno;
    }

    public void setCargoFirmaUno(String cargoFirmaUno) {
        this.cargoFirmaUno = cargoFirmaUno;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Municipio getCiudad() {
        return ciudad;
    }

    public void setCiudad(Municipio ciudad) {
        this.ciudad = ciudad;
    }

    public Date getFechaDocumento() {
        return fechaDocumento;
    }

    public void setFechaDocumento(Date fechaDocumento) {
        this.fechaDocumento = fechaDocumento;
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getCargoFirmaDos() {
        return cargoFirmaDos;
    }

    public void setCargoFirmaDos(String cargoFirmaDos) {
        this.cargoFirmaDos = cargoFirmaDos;
    }

    @XmlTransient
    @JsonIgnore
    public List<CircularRemInterno> getCircularRemInternoList() {
        return circularRemInternoList;
    }

    public void setCircularRemInternoList(List<CircularRemInterno> circularRemInternoList) {
        this.circularRemInternoList = circularRemInternoList;
    }

    public Documento getDocumentoFirmado() {
        return documentoFirmado;
    }

    public void setDocumentoFirmado(Documento documentoFirmado) {
        this.documentoFirmado = documentoFirmado;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
