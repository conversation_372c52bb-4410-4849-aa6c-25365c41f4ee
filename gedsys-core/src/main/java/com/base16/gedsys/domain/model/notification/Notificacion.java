/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.notification;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "notificacion", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Notificacion.findAll", query = "SELECT n FROM Notificacion n")
    , @NamedQuery(name = "Notificacion.findById", query = "SELECT n FROM Notificacion n WHERE n.id = :id")
    , @NamedQuery(name = "Notificacion.findByAsunto", query = "SELECT n FROM Notificacion n WHERE n.asunto = :asunto")
    , @NamedQuery(name = "Notificacion.findByCreadorPor", query = "SELECT n FROM Notificacion n WHERE n.creadorPor = :creadorPor")
    , @NamedQuery(name = "Notificacion.findByDiasNotificacion", query = "SELECT n FROM Notificacion n WHERE n.diasNotificacion = :diasNotificacion")
    , @NamedQuery(name = "Notificacion.findByFechaCreacion", query = "SELECT n FROM Notificacion n WHERE n.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Notificacion.findByFechaModificacion", query = "SELECT n FROM Notificacion n WHERE n.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Notificacion.findByFechaNotificacion", query = "SELECT n FROM Notificacion n WHERE n.fechaNotificacion between :startDate and :endDate")
    , @NamedQuery(name = "Notificacion.findByMesesNotificacion", query = "SELECT n FROM Notificacion n WHERE n.mesesNotificacion = :mesesNotificacion")
    , @NamedQuery(name = "Notificacion.findByModificadoPor", query = "SELECT n FROM Notificacion n WHERE n.modificadoPor = :modificadoPor")
    , @NamedQuery(name = "Notificacion.findByNotificacionCorreo", query = "SELECT n FROM Notificacion n WHERE n.notificacionCorreo = :notificacionCorreo")
    , @NamedQuery(name = "Notificacion.findByNotificacionPeriodica", query = "SELECT n FROM Notificacion n WHERE n.notificacionPeriodica = :notificacionPeriodica")
    , @NamedQuery(name = "Notificacion.findByNotificacionPopup", query = "SELECT n FROM Notificacion n WHERE n.notificacionPopup = :notificacionPopup")
    , @NamedQuery(name = "Notificacion.findByNotificacionPush", query = "SELECT n FROM Notificacion n WHERE n.notificacionPush = :notificacionPush")
    , @NamedQuery(name = "Notificacion.findByResponsable", query = "SELECT n FROM Notificacion n WHERE n.responsable = :responsable AND n.visible = :visible")
    , @NamedQuery(name = "Notificacion.findNotificacionByDateDocument", query = "SELECT n FROM Notificacion n WHERE n.documento = :documento AND n.fechaNotificacion >:fechaDocumento order by n.id ASC")
    , @NamedQuery(name = "Notificacion.findByDocumento", query = "SELECT n FROM Notificacion n WHERE n.documento = :documento")    
    , @NamedQuery(name = "Notificacion.findByResponsableAndToday", query = "SELECT n FROM Notificacion n WHERE n.responsable = :responsable and n.estado = true and n.visible = 1 and n.notificacionPopup = true and n.fechaNotificacion <= :FechaActual order by n.id DESC")
    , @NamedQuery(name = "Notificacion.findByDestinatarioAndToday", query = "SELECT n FROM Notificacion n JOIN n.documento d WHERE d.destinatario = :responsable and n.estado = true and n.visible = 2 and n.responsable is null and  n.fechaNotificacion <= :FechaActual order by n.id DESC")
    , @NamedQuery(name = "Notificacion.findByResponsableAndTodayNotificacion", query = "SELECT n FROM Notificacion n WHERE n.responsable = :responsable and n.estado = true and n.visible = 2 and n.fechaNotificacion <= :FechaActual order by n.id DESC")
    /* , @NamedQuery(name = "Notificacion.findByResponsableAndToday", query = "SELECT n FROM Notificacion n WHERE n.responsable = :responsable and n.estado = 3 and TIME(n.fechaNotificacion) = (SELECT MIN(TIME(n2.fechaNotificacion)) FROM Notificacion n2) and DATE(n.fechaNotificacion) = :fechaActual")*/

    , @NamedQuery(name = "Notificacion.findByPeridicidadNotificacion", query = "SELECT n FROM Notificacion n WHERE n.peridicidadNotificacion = :peridicidadNotificacion")
    , @NamedQuery(name = "Notificacion.findByTipoPeriodo", query = "SELECT n FROM Notificacion n WHERE n.tipoPeriodo = :tipoPeriodo")})
public class Notificacion implements Serializable {

    @JoinColumn(name = "Documento", referencedColumnName = "Id")
    @ManyToOne
    private Documento documento;

    @Column(name = "visible")
    private Integer visible;

    @Column(name = "estado")
    private Boolean estado;

    @Basic(optional = false)
    @Column(name = "CreadorPor")
    private int creadorPor;
    @Column(name = "PeridicidadNotificacion")
    private Boolean peridicidadNotificacion;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Long id;
    @Column(name = "Asunto")
    private String asunto;
    @Lob
    @Column(name = "Descripcion")
    private String descripcion;
    @Column(name = "DiasNotificacion")
    private Integer diasNotificacion;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "FechaNotificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaNotificacion;
    @Column(name = "MesesNotificacion")
    private Integer mesesNotificacion;
    @Column(name = "ModificadoPor")
    private Integer modificadoPor;
    @Column(name = "NotificacionCorreo")
    private Boolean notificacionCorreo;
    @Column(name = "NotificacionPeriodica")
    private Boolean notificacionPeriodica;
    @Column(name = "NotificacionPopup")
    private Boolean notificacionPopup;
    @Column(name = "NotificacionPush")
    private Boolean notificacionPush;
    @Column(name = "TipoPeriodo")
    private Integer tipoPeriodo;
    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;
    @Column(name = "FechaInicio")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaInicio;
    @Column(name = "FechaFinalizacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFinalizacion;
    @Column(name = "NumeroPeriodos")
    private Integer numeroPeriodos;
    @Column(name = "tipo_borrador")
    private String tipoBorrador;
    @Column(name = "id_borrador")
    private Integer idBorrador;

    public Notificacion() {
    }

    public Notificacion(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public Integer getDiasNotificacion() {
        return diasNotificacion;
    }

    public void setDiasNotificacion(Integer diasNotificacion) {
        this.diasNotificacion = diasNotificacion;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Date getFechaNotificacion() {
        return fechaNotificacion;
    }

    public void setFechaNotificacion(Date fechaNotificacion) {
        this.fechaNotificacion = fechaNotificacion;
    }

    public Integer getMesesNotificacion() {
        return mesesNotificacion;
    }

    public void setMesesNotificacion(Integer mesesNotificacion) {
        this.mesesNotificacion = mesesNotificacion;
    }

    public Integer getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Integer modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Boolean getNotificacionCorreo() {
        return notificacionCorreo;
    }

    public void setNotificacionCorreo(Boolean notificacionCorreo) {
        this.notificacionCorreo = notificacionCorreo;
    }

    public Boolean getNotificacionPeriodica() {
        return notificacionPeriodica;
    }

    public void setNotificacionPeriodica(Boolean notificacionPeriodica) {
        this.notificacionPeriodica = notificacionPeriodica;
    }

    public Boolean getNotificacionPopup() {
        return notificacionPopup;
    }

    public void setNotificacionPopup(Boolean notificacionPopup) {
        this.notificacionPopup = notificacionPopup;
    }

    public Boolean getNotificacionPush() {
        return notificacionPush;
    }

    public void setNotificacionPush(Boolean notificacionPush) {
        this.notificacionPush = notificacionPush;
    }

    public Integer getTipoPeriodo() {
        return tipoPeriodo;
    }

    public void setTipoPeriodo(Integer tipoPeriodo) {
        this.tipoPeriodo = tipoPeriodo;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Notificacion)) {
            return false;
        }
        Notificacion other = (Notificacion) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.Notificacion[ id=" + id + " ]";
    }

    public Date getFechaInicio() {
        return fechaInicio;
    }

    public void setFechaInicio(Date fechaInicio) {
        this.fechaInicio = fechaInicio;
    }

    public Date getFechaFinalizacion() {
        return fechaFinalizacion;
    }

    public void setFechaFinalizacion(Date fechaFinalizacion) {
        this.fechaFinalizacion = fechaFinalizacion;
    }

    public Integer getNumeroPeriodos() {
        return numeroPeriodos;
    }

    public void setNumeroPeriodos(Integer numeroPeriodos) {
        this.numeroPeriodos = numeroPeriodos;
    }

    public int getCreadorPor() {
        return creadorPor;
    }

    public void setCreadorPor(int creadorPor) {
        this.creadorPor = creadorPor;
    }

    public Boolean getPeridicidadNotificacion() {
        return peridicidadNotificacion;
    }

    public void setPeridicidadNotificacion(Boolean peridicidadNotificacion) {
        this.peridicidadNotificacion = peridicidadNotificacion;
    }

    public Boolean getEstado() {
        return estado;
    }

    public void setEstado(Boolean estado) {
        this.estado = estado;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    public Documento getDocumento() {
        return documento;
    }

    public void setDocumento(Documento documento) {
        this.documento = documento;
    }


    public String getTipoBorrador() {
        return tipoBorrador;
    }

    public void setTipoBorrador(String tipoBorrador) {
        this.tipoBorrador = tipoBorrador;
    }

    public Integer getIdBorrador() {
        return idBorrador;
    }

    public void setIdBorrador(Integer idBorrador) {
        this.idBorrador = idBorrador;
    }

}
