package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.document.Consecutivo;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;

public class ConsecutivoListener {

    @PrePersist
    public void setCreated(Consecutivo c) {
        c.setFechaCreacion(new Date());
    }

    @PreUpdate
    public  void setUpdated(Consecutivo c) {
        c.setFechaModificacion(new Date());
    }
}
