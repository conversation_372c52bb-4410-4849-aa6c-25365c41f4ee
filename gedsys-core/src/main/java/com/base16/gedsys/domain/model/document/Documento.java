/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.archive.*;
import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.entityListeners.DocumentListener;
import com.base16.gedsys.domain.model.location.Corregimiento;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.location.Vereda;
import com.base16.gedsys.domain.model.notification.Notificacion;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.domain.model.sendTemplate.PlanillaEnvioDocumento;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "documento", catalog = "", schema = "")
@XmlRootElement
@EntityListeners(DocumentListener.class)
@NamedQueries({
        @NamedQuery(name = "Documento.findAll", query = "SELECT d FROM Documento d")
        , @NamedQuery(name = "Documento.findDestinatarioAll", query = "SELECT d FROM Documento d WHERE d.destinatario = :destinatario")
        , @NamedQuery(name = "Documento.findById", query = "SELECT d FROM Documento d WHERE d.id = :id")
        , @NamedQuery(name = "Documento.findByAnexos", query = "SELECT d FROM Documento d WHERE d.anexos = :anexos")
        , @NamedQuery(name = "Documento.findByTransportador", query = "SELECT d FROM Documento d WHERE d.transportador = :transportador")
        , @NamedQuery(name = "Documento.findByEntidad", query = "SELECT d FROM Documento d WHERE d.entidad = :entidad")
        , @NamedQuery(name = "Documento.findByTipoDocumental", query = "SELECT d FROM Documento d WHERE d.tipoDocumental = :tipoDoc")
        , @NamedQuery(name = "Documento.findByAsunto", query = "SELECT d FROM Documento d WHERE d.asunto = :asunto")
        , @NamedQuery(name = "Documento.findByCodigoPostal", query = "SELECT d FROM Documento d WHERE d.codigoPostal = :codigoPostal")
        , @NamedQuery(name = "Documento.findByConsecutivo", query = "SELECT d FROM Documento d WHERE d.consecutivo = :consecutivo")
        , @NamedQuery(name = "Documento.findByRadicadoEnvio", query = "SELECT d FROM Documento d WHERE d.radicadoEnvio = :radicadoEnvio")
        , @NamedQuery(name = "Documento.findByRadicadoEnvioAndIdDocument", query = "SELECT d FROM Documento d WHERE d.radicadoEnvio = :radicadoEnvio AND d.id = :idDocumento")
        , @NamedQuery(name = "Documento.findByConsecutivoAndIdDocument", query = "SELECT d FROM Documento d WHERE d.consecutivo = :consecutivo AND d.id = :idDocumento")
        , @NamedQuery(name = "Documento.buscardocumentoIdyEstado", query = "SELECT d FROM Documento d WHERE d.id = :id ")
        , @NamedQuery(name = "Documento.findAlPadre", query = "SELECT d FROM Documento d WHERE d.documentoRelacionado = :hijo")
        , @NamedQuery(name = "Documento.findByCompartidos", query = "SELECT d FROM Documento d JOIN d.destinatariosDocCollection c WHERE (c.destinatarioId = :destinatario)")
        , @NamedQuery(name = "Documento.findByUsuarioAuditorDocCompartidos", query = "SELECT d FROM Documento d WHERE EXISTS (SELECT dd.documentoId FROM DestinatariosDocumento dd WHERE ((dd.destinatarioId IN :usuarios) or (dd.destinatarioId =:usuarioAuditor)) and d = dd.documentoId)")
        , @NamedQuery(name = "Documento.findByCompartidosCon", query = "SELECT d FROM Documento d JOIN d.destinatariosDocCollection c WHERE (c.creadoPor = :autor)")
        , @NamedQuery(name = "Documento.findEntrantes", query = "SELECT d FROM Documento d WHERE (d.destinatario = :destinatario or d.destinatarioInterno = :destinatario) and (d.estado = 2 or d.estado = 1 or d.estado = 3 or d.estado = 12) AND d.documentoRelacionado is null")
        , @NamedQuery(name = "Documento.findEntrantesByState", query = "SELECT d FROM Documento d WHERE (d.consecutivo IN :consecutivos) AND (d.estado = 2 or d.estado = 1) AND (d.documentoRelacionado is null) ORDER BY d.fechaCreacion ASC")
        , @NamedQuery(name = "Documento.findByArrayDocumento", query = "SELECT d FROM Documento d WHERE (d.id NOT IN :documentos) AND d.estado = 2 AND d.requiereRespuesta = true AND d.seccion =:seccion")
        , @NamedQuery(name = "Documento.findByDocEntranteSolicitudConsec", query = "SELECT d FROM Documento d WHERE d.estado = 2 AND d.requiereRespuesta = true AND d.seccion =:seccion")
        , @NamedQuery(name = "Documento.findByDocumentoEntrante", query = "SELECT d FROM Documento d WHERE d.estado = 2 AND d.requiereRespuesta = true")
        , @NamedQuery(name = "Documento.findEntrantesInternos", query = "SELECT d FROM Documento d WHERE d.destinatarioInterno = :destinatarioInterno and (d.estado = 12)")
        , @NamedQuery(name = "Documento.findPorEnviar", query = "SELECT d FROM Documento d WHERE  (d.estado = 8 or d.estado = 7 or d.estado = 21) ORDER BY d.fechaCreacion ASC")
        , @NamedQuery(name = "Documento.findPorEnviarPlanilla", query = "SELECT d FROM Documento d WHERE  (d.estado = 8 or d.estado = 7) and (d.radicadoEnvio is not null) ORDER BY d.radicadoEnvio ASC")
        , @NamedQuery(name = "Documento.findEnviados", query = "SELECT d FROM Documento d WHERE (d.destinatario = :destinatario or d.creadoPor = :destinatario or d.radicadoPor = :destinatario) and d.estado = 9")
        , @NamedQuery(name = "Documento.findEnPrestamo", query = "SELECT d FROM Documento d WHERE d.destinatario = :destinatario and (d.estado = 3 or d.estado = 4 or d.estado = 5)")
        , @NamedQuery(name = "Documento.findSinArchivar", query = "SELECT d FROM Documento d JOIN d.destinatario u WHERE u.seccion = :seccion and (d.estado = 1 or d.estado = 2 or d.estado = 10 or d.estado = 12) and (d.archivado = false or d.archivado is null)")
        , @NamedQuery(name = "Documento.findSinArchivarByVentanilla", query = "SELECT d FROM Documento d JOIN d.destinatario u WHERE (d.estado = 1 or d.estado = 2 or d.estado = 10 or d.estado = 12) and (d.archivado = false or d.archivado is null)")
        , @NamedQuery(name = "Documento.findPorVencer", query = "SELECT d FROM Documento d WHERE d.destinatario = :destinatario and (d.estado = 2 or d.estado = 4 or d.estado = 1 ) and d.requiereRespuesta = true ")
        , @NamedQuery(name = "Documento.findPorVencerSalientes", query = "SELECT d FROM Documento d WHERE (d.estado = 13 or d.estado = 11) and d.requiereRespuesta = true ")
        , @NamedQuery(name = "Documento.findByDireccion", query = "SELECT d FROM Documento d WHERE d.direccion = :direccion")
        , @NamedQuery(name = "Documento.findByEstado", query = "SELECT d FROM Documento d WHERE d.estado = :estado")
        , @NamedQuery(name = "Documento.findByExtension", query = "SELECT d FROM Documento d WHERE d.extension = :extension")
        , @NamedQuery(name = "Documento.findByFechaCreacion", query = "SELECT d FROM Documento d WHERE d.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "Documento.findByFechaDocumento", query = "SELECT d FROM Documento d WHERE d.fechaDocumento = :fechaDocumento")
        , @NamedQuery(name = "Documento.findByFechaModificacion", query = "SELECT d FROM Documento d WHERE d.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "Documento.findByFolioNro", query = "SELECT d FROM Documento d WHERE d.folioNro = :folioNro")
        , @NamedQuery(name = "Documento.findByFolios", query = "SELECT d FROM Documento d WHERE d.folios = :folios")
        , @NamedQuery(name = "Documento.findByLibros", query = "SELECT d FROM Documento d WHERE d.libros = :libros")
        , @NamedQuery(name = "Documento.findByMedioEnvio", query = "SELECT d FROM Documento d WHERE d.medioEnvio = :medioEnvio")
        , @NamedQuery(name = "Documento.findByMimeType", query = "SELECT d FROM Documento d WHERE d.mimeType = :mimeType")
        , @NamedQuery(name = "Documento.findByNombreDocumento", query = "SELECT d FROM Documento d WHERE d.nombreDocumento = :nombreDocumento")
        , @NamedQuery(name = "Documento.findByPathFile", query = "SELECT d FROM Documento d WHERE d.pathFile = :pathFile")
        , @NamedQuery(name = "Documento.findByRemitente", query = "SELECT d FROM Documento d WHERE d.remitente = :remitente")
        , @NamedQuery(name = "Documento.findByRequiereRespuesta", query = "SELECT d FROM Documento d WHERE d.requiereRespuesta = :requiereRespuesta")
        , @NamedQuery(name = "Documento.findRadicados", query = "SELECT d FROM Documento d WHERE d.creadoPor = :creadoPor")
        , @NamedQuery(name = "Documento.findByUnidadDocArchivado", query = "SELECT d FROM Documento d WHERE d.unidadDocumental = :unidadDoc and d.estado = 10")
        , @NamedQuery(name = "Documento.findByTransportadorNotNull", query = "SELECT d FROM Documento d WHERE (d.estado = 8 or d.estado = 7) AND d.transportador is not null")
//        , @NamedQuery(name = "Documento.findDocumentos", query = "SELECT d FROM Documento d WHERE d.id = :idDocumento and (d.consecutivo like :consecutivo or d.asunto like :asunto or d.fechaCreacion between :startDate and :endDate or d.tipoDocumental =:tipoDocumental or d.municipio =:municipio or d.remitente =:remitente) ")
        , @NamedQuery(name = "Documento.findByRutaArchivo", query = "SELECT d FROM Documento d WHERE d.rutaArchivo = :rutaArchivo")
        , @NamedQuery(name = "Documento.findByNietosGuia", query = "SELECT d FROM Documento d WHERE d.guia = :guia")
        , @NamedQuery(name = "Documento.findNietosByGuiaOrArchivo", query = "SELECT d FROM Documento d WHERE d.guia = :guia OR d.comprobante = :comprobante")
        , @NamedQuery(name = "Documento.findByNietosComprobante", query = "SELECT d FROM Documento d WHERE d.comprobante = :comprobante")
        , @NamedQuery(name = "Documento.findDocByReporteTipoDoc", query = "SELECT t.nombre,d.fechaRecepcion,d.fechaVencimiento,d.fechaDocumento,d.requiereRespuesta FROM Documento d left join d.documentoRelacionado dr left join dr.tipoDocumental t")
        , @NamedQuery(name = "Documento.findEntrantesPQRSF", query = "SELECT d FROM Documento d JOIN d.tipoDocumento t WHERE d.destinatario = :destinatario and t.esPQRSF = true and (d.estado = 1 or d.estado = 7)")
        , @NamedQuery(name = "Documento.findPorVencerPQRSF", query = "SELECT d FROM Documento d JOIN d.tipoDocumento t WHERE d.destinatario = :destinatario and d.estado = 1 and d.requiereRespuesta = true and t.esPQRSF = true")
        , @NamedQuery(name = "Documento.findSinArchivarPQRSF", query = "SELECT d FROM Documento d JOIN d.tipoDocumento t WHERE d.destinatario = :destinatario and d.estado = 3 or d.estado = 9 and t.esPQRSF = true")
        , @NamedQuery(name = "Documento.buscarDocumentosXSeccionyFechas", query = "SELECT d FROM Documento d WHERE d.seccion = :seccion AND d.estado <> 40 AND d.fechaCreacion BETWEEN :fechaInicio AND :fechaFin or d.consecutivo = :consecutivo")
})
//SELECT d FROM Documento d INNER JOIN tipodocumento td ON (td.id = d.TipoDocumento) WHERE td.EsPQRSF = 1

public class Documento implements Serializable {

    @OneToMany(mappedBy = "documento")
    private List<Anexos> anexosList;

    @Column(name = "comunicacionInterna")
    private Boolean comunicacionInterna;

    @Column(name = "firmaIncompleta")
    private Boolean firmaIncompleta;

    @OneToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinColumn(name = "pqrsfGeneral", referencedColumnName = "Id")
    private IncidenciaGeneral incidenciaRespondida;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "email")
    private String email;

    @Column(name = "banderaAuditor")
    private Boolean banderaAuditor;

    @Column(name = "orden")
    private Integer orden;

    @Column(name = "isCargaDoc")
    private Integer isCargaDoc;

    
    @Transient
    String isDestinatariosDocumento;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Long id;
    @Column(name = "Anexos")
    private Boolean anexos;
    @Column(name = "Asunto")
    private String asunto;
    @Column(name = "CodigoPostal")
    private String codigoPostal;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Lob
    @Column(name = "Detalle")
    private String detalle;
    @Column(name = "Direccion")
    private String direccion;
    @Column(name = "Estado")
    private Integer estado;
    @Column(name = "Extension")
    private String extension;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaDocumento")
    @Temporal(TemporalType.DATE)
    private Date fechaDocumento;
    @Column(name = "FechaModificacion")
    private Date fechaModificacion;
    @Column(name = "FolioNro")
    private Integer folioNro;
    @Column(name = "Libros")
    private String libros;
    @Column(name = "MimeType")
    private String mimeType;
    @Column(name = "NombreDocumento")
    private String nombreDocumento;
    @Column(name = "PathFile")
    private String pathFile;
    @Column(name = "Remitente")
    private String remitente;
    @Column(name = "ConCopiaExternos")
    private String conCopiaExternos;
    @Column(name = "RequiereRespuesta")
    private Boolean requiereRespuesta;
    @Column(name = "RutaArchivo")
    private String rutaArchivo;
    @Column(name = "FechaEnvio")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaEnvio;
    @Column(name = "FechaRecepcion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRecepcion;
    @Column(name = "RemitenteExteno")
    private String remitenteExteno;
    @Column(name = "Clase")
    private String clase;
    @Column(name = "FechaVencimiento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaVencimiento;
    @Column(name = "RutaGuia")
    private String rutaGuia;
    @Column(name = "Guia")
    private String guia;
    @Column(name = "RutaComprobante")
    private String rutaComprobante;
    @Column(name = "RadicadoEnvio")
    private String radicadoEnvio;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "Destinatario", referencedColumnName = "Id")
    @ManyToOne
    private Usuario destinatario;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "DestinatarioInterno", referencedColumnName = "Id")
    @ManyToOne
    private Usuario destinatarioInterno;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "Autor", referencedColumnName = "Id")
    @ManyToOne
    private Autor autor;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "ClaseDocumento", referencedColumnName = "id")
    @ManyToOne
    private ClaseDocumento claseDocumento;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "Corregimiento", referencedColumnName = "Id")
    @ManyToOne
    private Corregimiento corregimiento;

    @XmlTransient
    @JsonIgnore
    @OneToMany(mappedBy = "documentoRelacionado")
    private Collection<Documento> documentoCollection;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "DocumentoRelacionado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoRelacionado;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "Entidad", referencedColumnName = "id")
    @ManyToOne
    private Entidad entidad;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "TipoDocumento", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumento tipoDocumento;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "Municipio", referencedColumnName = "Id")
    @ManyToOne
    private Municipio municipio;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "Transportador", referencedColumnName = "Id")
    @ManyToOne
    private Transportador transportador;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "SignaturaTopografica", referencedColumnName = "Id")
    @ManyToOne
    private SignaturaTopografica signaturaTopografica;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;

    @XmlTransient
    @JsonIgnore
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "documentoId")
    private Collection<DestinatariosDocumento> destinatariosDocCollection;

    @XmlTransient
    @JsonIgnore
    @OneToMany(mappedBy = "documento")
    private Collection<ProcesoDocumental> procesodocumentalCollection;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "MedioEnvio", referencedColumnName = "Id")
    @ManyToOne
    private Mediorecepcion medioEnvio;

    @OneToMany(mappedBy = "documentoPadre")
    private List<Carta> cartaList;

    @Column(name = "Comprobante")
    private String comprobante;

    @Column(name = "bandera")
    private Boolean bandera;

    @XmlTransient
    @JsonIgnore
    @OneToMany(mappedBy = "documento")
    private List<Comentario> comentarioList;

    @Column(name = "Folios")
    private Integer folios;

    @Column(name = "FolioInicial")
    private Integer folioInicial;

    @Basic(optional = false)
    @NotNull
    @Column(name = "NoProducidoPorGedsys")
    private boolean noProducidoPorGedsys;

    @Size(max = 250)
    @Column(name = "numeroGuia")
    private String numeroGuia;

    @Size(max = 255)
    @Column(name = "AnexoDocumento")
    private String anexoDocumento;

    @Size(max = 255)
    @Column(name = "RutaAnexo")
    private String rutaAnexo;

    @Column(name = "numpaginas")
    private Integer numpaginas;

    @JoinColumn(name = "Seccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccion;

    @Size(max = 255)
    @Column(name = "hash")
    private String hash;

    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;

    @OneToMany(mappedBy = "documento")
    private List<Trazabilidad> trazabilidadList;

    @Column(name = "Archivado")
    private Boolean archivado;

    @OneToMany(mappedBy = "documento")
    private List<Notificacion> notificacionList;

    @JoinColumn(name = "RadicadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario radicadoPor;

    @Column(name = "Dias")
    private Integer dias;
    @Column(name = "ConsecExterno")
    private String consecExterno;
    @OneToMany(mappedBy = "documento")
    private List<MensajeriaDocumental> mensajeriaDocumentalList;

    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Size(max = 255)
    @Column(name = "Valor")
    private String valor;

    @JoinColumn(name = "UnidadDocumental", referencedColumnName = "Id")
    @ManyToOne
    private UnidadDocumental unidadDocumental;

    @JoinColumn(name = "Sede", referencedColumnName = "Id")
    @ManyToOne
    private Sede sede;
    @JoinColumn(name = "Vereda", referencedColumnName = "Id")
    @ManyToOne
    private Vereda vereda;

    @OneToMany(mappedBy = "documento")
    private List<Prestamo> prestamoList;

    @OneToMany(mappedBy = "documento")
    private List<PlanillaEnvioDocumento> planillaEnvioDocumentoList;

    public Documento() {
    }

    public Documento(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getAnexos() {
        return anexos;
    }

    public void setAnexos(Boolean anexos) {
        this.anexos = anexos;
    }

    public Boolean getBandera() {
        return bandera;
    }

    public void setBandera(Boolean bandera) {
        this.bandera = bandera;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getCodigoPostal() {
        return codigoPostal;
    }

    public void setCodigoPostal(String codigoPostal) {
        this.codigoPostal = codigoPostal;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    @XmlTransient
    @JsonIgnore
    public Usuario getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(Usuario destinatario) {
        this.destinatario = destinatario;
    }

    public String getDetalle() {
        return detalle;
    }

    public void setDetalle(String detalle) {
        this.detalle = detalle;
    }

    public Integer getIsCargaDoc() {
        return isCargaDoc;
    }

    public void setIsCargaDoc(Integer isCargaDoc) {
        this.isCargaDoc = isCargaDoc;
    }

    public String getDireccion() {
        return direccion;
    }

    public void setDireccion(String direccion) {
        this.direccion = direccion;
    }

    public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaDocumento() {
        return fechaDocumento;
    }

    public void setFechaDocumento(Date fechaDocumento) {
        this.fechaDocumento = fechaDocumento;
    }

    public Date getFechaModificacion() {
        return new Date();
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Integer getFolioNro() {
        return folioNro;
    }

    public void setFolioNro(Integer folioNro) {
        this.folioNro = folioNro;
    }

    public String getLibros() {
        return libros;
    }

    public void setLibros(String libros) {
        this.libros = libros;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getNombreDocumento() {
        return nombreDocumento;
    }

    public void setNombreDocumento(String nombreDocumento) {
        this.nombreDocumento = nombreDocumento;
    }

    public String getPathFile() {
        return pathFile;
    }

    public void setPathFile(String pathFile) {
        this.pathFile = pathFile;
    }

    public String getRemitente() {
        return remitente;
    }

    public void setRemitente(String remitente) {
        this.remitente = remitente;
    }

    public Boolean getRequiereRespuesta() {
        return requiereRespuesta;
    }

    public void setRequiereRespuesta(Boolean requiereRespuesta) {
        this.requiereRespuesta = requiereRespuesta;
    }

    public String getRutaArchivo() {
        return rutaArchivo;
    }

    public void setRutaArchivo(String rutaArchivo) {
        this.rutaArchivo = rutaArchivo;
    }

    public String getRutaComprobante() {
        return rutaComprobante;
    }

    public void setRutaComprobante(String rutaComprobante) {
        this.rutaComprobante = rutaComprobante;
    }

    @XmlTransient
    @JsonIgnore
    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Autor getAutor() {
        return autor;
    }

    public void setAutor(Autor autor) {
        this.autor = autor;
    }

    public ClaseDocumento getClaseDocumento() {
        return claseDocumento;
    }

    public void setClaseDocumento(ClaseDocumento claseDocumento) {
        this.claseDocumento = claseDocumento;
    }

    public Corregimiento getCorregimiento() {
        return corregimiento;
    }

    public void setCorregimiento(Corregimiento corregimiento) {
        this.corregimiento = corregimiento;
    }

    public String getClase() {
        return clase;
    }

    public void setClase(String clase) {
        this.clase = clase;
    }

    public Date getFechaEnvio() {
        return fechaEnvio;
    }

    public void setFechaEnvio(Date fechaEnvio) {
        this.fechaEnvio = fechaEnvio;
    }

    public Date getFechaRecepcion() {
        return fechaRecepcion;
    }

    public void setFechaRecepcion(Date fechaRecepcion) {
        this.fechaRecepcion = fechaRecepcion;
    }

    public String getRutaGuia() {
        return rutaGuia;
    }

    public void setRutaGuia(String rutaGuia) {
        this.rutaGuia = rutaGuia;
    }

    public String getGuia() {
        return guia;
    }

    public void setGuia(String guia) {
        this.guia = guia;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<Documento> getDocumentoCollection() {
        return documentoCollection;
    }

    public void setDocumentoCollection(Collection<Documento> documentoCollection) {
        this.documentoCollection = documentoCollection;
    }

    public Documento getDocumentoRelacionado() {
        return documentoRelacionado;
    }

    public void setDocumentoRelacionado(Documento documentoRelacionado) {
        this.documentoRelacionado = documentoRelacionado;
    }

    public Entidad getEntidad() {
        return entidad;
    }

    public void setEntidad(Entidad entidad) {
        this.entidad = entidad;
    }

    @XmlTransient
    @JsonIgnore
    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public TipoDocumento getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(TipoDocumento tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public String getIsDestinatariosDocumento() {
        return isDestinatariosDocumento;
    }

    public void setIsDestinatariosDocumento(String isDestinatariosDocumento) {
        this.isDestinatariosDocumento = isDestinatariosDocumento;
    }

    public Municipio getMunicipio() {
        return municipio;
    }

    public void setMunicipio(Municipio municipio) {
        this.municipio = municipio;
    }

    public Transportador getTransportador() {
        return transportador;
    }

    public void setTransportador(Transportador transportador) {
        this.transportador = transportador;
    }

    public SignaturaTopografica getSignaturaTopografica() {
        return signaturaTopografica;
    }

    public void setSignaturaTopografica(SignaturaTopografica signaturaTopografica) {
        this.signaturaTopografica = signaturaTopografica;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<DestinatariosDocumento> getDestinatariosDocCollection() {
        return destinatariosDocCollection;
    }

    public void setDestinatariosDocCollection(Collection<DestinatariosDocumento> destinatariosDocCollection) {
        this.destinatariosDocCollection = destinatariosDocCollection;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<ProcesoDocumental> getProcesodocumentalCollection() {
        return procesodocumentalCollection;
    }

    public void setProcesodocumentalCollection(Collection<ProcesoDocumental> procesodocumentalCollection) {
        this.procesodocumentalCollection = procesodocumentalCollection;
    }

    public Usuario getDestinatarioInterno() {
        return destinatarioInterno;
    }

    public void setDestinatarioInterno(Usuario destinatarioInterno) {
        this.destinatarioInterno = destinatarioInterno;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Documento)) {
            return false;
        }
        Documento other = (Documento) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.Documento[ id=" + id + " ]";
    }

    public String getRemitenteExteno() {
        return remitenteExteno;
    }

    public void setRemitenteExteno(String remitenteExteno) {
        this.remitenteExteno = remitenteExteno;
    }

    public Mediorecepcion getMedioEnvio() {
        return medioEnvio;
    }

    public void setMedioEnvio(Mediorecepcion medioEnvio) {
        this.medioEnvio = medioEnvio;
    }

    public Date getFechaVencimiento() {
        return fechaVencimiento;
    }

    public void setFechaVencimiento(Date fechaVencimiento) {
        this.fechaVencimiento = fechaVencimiento;
    }

    @XmlTransient
    @JsonIgnore
    public List<Comentario> getComentarioList() {
        return comentarioList;
    }

    public void setComentarioList(List<Comentario> comentarioList) {
        this.comentarioList = comentarioList;
    }

    public String getComprobante() {
        return comprobante;
    }

    public void setComprobante(String comprobante) {
        this.comprobante = comprobante;
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    public String getRadicadoEnvio() {
        return radicadoEnvio;
    }

    public void setRadicadoEnvio(String radicadoEnvio) {
        this.radicadoEnvio = radicadoEnvio;
    }

    @XmlTransient
    @JsonIgnore
    public List<PlanillaEnvioDocumento> getPlanillaEnvioDocumentoList() {
        return planillaEnvioDocumentoList;
    }

    public void setPlanillaEnvioDocumentoList(List<PlanillaEnvioDocumento> planillaEnvioDocumentoList) {
        this.planillaEnvioDocumentoList = planillaEnvioDocumentoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Prestamo> getPrestamoList() {
        return prestamoList;
    }

    public void setPrestamoList(List<Prestamo> prestamoList) {
        this.prestamoList = prestamoList;
    }

    public Sede getSede() {
        return sede;
    }

    public void setSede(Sede sede) {
        this.sede = sede;
    }

    public Vereda getVereda() {
        return vereda;
    }

    public void setVereda(Vereda vereda) {
        this.vereda = vereda;
    }

    public UnidadDocumental getUnidadDocumental() {
        return unidadDocumental;
    }

    public void setUnidadDocumental(UnidadDocumental unidadDocumental) {
        this.unidadDocumental = unidadDocumental;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public String getConsecExterno() {
        return consecExterno;
    }

    public void setConsecExterno(String consecExterno) {
        this.consecExterno = consecExterno;
    }

    @XmlTransient
    @JsonIgnore
    public List<MensajeriaDocumental> getMensajeriaDocumentalList() {
        return mensajeriaDocumentalList;
    }

    public void setMensajeriaDocumentalList(List<MensajeriaDocumental> mensajeriaDocumentalList) {
        this.mensajeriaDocumentalList = mensajeriaDocumentalList;
    }

    public Usuario getRadicadoPor() {
        return radicadoPor;
    }

    public void setRadicadoPor(Usuario radicadoPor) {
        this.radicadoPor = radicadoPor;
    }

    @XmlTransient
    @JsonIgnore
    public List<Notificacion> getNotificacionList() {
        return notificacionList;
    }

    public void setNotificacionList(List<Notificacion> notificacionList) {
        this.notificacionList = notificacionList;
    }

    public Boolean getArchivado() {
        return archivado;
    }

    public void setArchivado(Boolean archivado) {
        this.archivado = archivado;
    }

    @XmlTransient
    @JsonIgnore
    public List<Trazabilidad> getTrazabilidadList() {
        return trazabilidadList;
    }

    public void setTrazabilidadList(List<Trazabilidad> trazabilidadList) {
        this.trazabilidadList = trazabilidadList;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public SeccionSubSeccion getSeccion() {
        return seccion;
    }

    public void setSeccion(SeccionSubSeccion seccion) {
        this.seccion = seccion;
    }

    public Integer getNumpaginas() {
        return numpaginas;
    }

    public void setNumpaginas(Integer numpaginas) {
        this.numpaginas = numpaginas;
    }

    public String getAnexoDocumento() {
        return anexoDocumento;
    }

    public void setAnexoDocumento(String anexoDocumento) {
        this.anexoDocumento = anexoDocumento;
    }

    public String getRutaAnexo() {
        return rutaAnexo;
    }

    public void setRutaAnexo(String rutaAnexo) {
        this.rutaAnexo = rutaAnexo;
    }

    public String getNumeroGuia() {
        return numeroGuia;
    }

    public void setNumeroGuia(String numeroGuia) {
        this.numeroGuia = numeroGuia;
    }

    public boolean getNoProducidoPorGedsys() {
        return noProducidoPorGedsys;
    }

    public void setNoProducidoPorGedsys(boolean noProducidoPorGedsys) {
        this.noProducidoPorGedsys = noProducidoPorGedsys;
    }

    public Integer getFolioInicial() {
        return folioInicial;
    }

    public void setFolioInicial(Integer folioInicial) {
        this.folioInicial = folioInicial;
    }

    public Integer getFolios() {
        return folios;
    }

    public void setFolios(Integer folios) {
        this.folios = folios;
    }

    public Integer getOrden() {
        return orden;
    }

    public void setOrden(Integer orden) {
        this.orden = orden;
    }

    public Boolean getBanderaAuditor() {
        return banderaAuditor;
    }

    public void setBanderaAuditor(Boolean banderaAuditor) {
        this.banderaAuditor = banderaAuditor;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getFirmaIncompleta() {
        return Optional.ofNullable(firmaIncompleta).isPresent() ? firmaIncompleta : false;
    }

    public void setFirmaIncompleta(Boolean firmaIncompleta) {
        this.firmaIncompleta = Optional.ofNullable(firmaIncompleta).isPresent() ? firmaIncompleta : false;
    }

    public Boolean getComunicacionInterna() {
        return Optional.ofNullable(comunicacionInterna).isPresent() ? comunicacionInterna : false;
    }

    public void setComunicacionInterna(Boolean comunicacionInterna) {
        this.comunicacionInterna = Optional.ofNullable(comunicacionInterna).isPresent() ? comunicacionInterna : false;
    }


    public String getConCopiaExternos() {
        return Optional.ofNullable(conCopiaExternos).isPresent() ? conCopiaExternos : "";
    }

    public void setConCopiaExternos(String conCopiaExternos) {
        this.conCopiaExternos = conCopiaExternos;
    }

   

    @XmlTransient
    @JsonIgnore
    public List<Anexos> getAnexosList() {
        return anexosList;
    }

    public void setAnexosList(List<Anexos> anexosList) {
        this.anexosList = anexosList;
    }

    public IncidenciaGeneral getIncidenciaRespondida() {
        return incidenciaRespondida;
    }

    public void setIncidenciaRespondida(IncidenciaGeneral incidenciaRespondida) {
        this.incidenciaRespondida = incidenciaRespondida;
    }
}
