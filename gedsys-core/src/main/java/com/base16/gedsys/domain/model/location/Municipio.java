/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.location;

import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.document.Entidad;
import com.base16.gedsys.domain.model.documentProducer.Acta;
import com.base16.gedsys.domain.model.documentProducer.Certificado;
import com.base16.gedsys.domain.model.documentProducer.Circular;
import com.base16.gedsys.domain.model.documentProducer.Comunicacion;
import com.base16.gedsys.domain.model.documentProducer.Constancia;
import com.base16.gedsys.domain.model.documentProducer.Informe;
import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "municipio", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
        @NamedQuery(name = "Municipio.findAll", query = "SELECT m FROM Municipio m ORDER BY m.nombre ASC")
        , @NamedQuery(name = "Municipio.findById", query = "SELECT m FROM Municipio m WHERE m.id = :id")
        , @NamedQuery(name = "Municipio.findByBorrado", query = "SELECT m FROM Municipio m WHERE m.borrado = :borrado")
        , @NamedQuery(name = "Municipio.findByCodigo", query = "SELECT m FROM Municipio m WHERE m.codigo = :codigo")
        , @NamedQuery(name = "Municipio.findByCodigoYDepartamento", query = "SELECT m FROM Municipio m WHERE m.codigo = :codigo and m.departamento = :departamento")
        , @NamedQuery(name = "Municipio.findByFechaCreacion", query = "SELECT m FROM Municipio m WHERE m.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "Municipio.findByFechaModificacion", query = "SELECT m FROM Municipio m WHERE m.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "Municipio.findByNombre", query = "SELECT m FROM Municipio m WHERE m.nombre = :nombre")
        , @NamedQuery(name = "Municipios.findByDepartamento", query = "SELECT m FROM Municipio m WHERE m.departamento = :departamento")
        , @NamedQuery(name = "Municipio.findByCodigoMunicipioDepartamento", query = "SELECT m FROM Municipio m WHERE m.codigo = :codigoMunicipio AND m.departamento.codigo = :codigoDpto")
})

public class Municipio implements Serializable {

    @OneToMany(mappedBy = "ciudad")
    private List<Informe> informeList;
    @OneToMany(mappedBy = "ciudad")
    private List<Circular> circularList;
    @OneToMany(mappedBy = "ciudad")
    private List<Certificado> certificadoList;
    @OneToMany(mappedBy = "ciudad")
    private List<Constancia> constanciaList;
    @OneToMany(mappedBy = "ciudad")
    private List<Comunicacion> comunicacionList;
    @Column(name = "deleted")
    private Boolean deleted;

    @OneToMany(mappedBy = "municipio")
    private List<Acta> actaList;

    @OneToMany(mappedBy = "municipio")
    private List<Entidad> entidadList;

    @OneToMany(mappedBy = "municipio")
    private List<Vereda> veredaList;

    @OneToMany(mappedBy = "ciudad")
    private List<Carta> cartaList;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "Codigo")
    private String codigo;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @OneToMany(mappedBy = "municipio")
    private Collection<Documento> documentoCollection;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "Departamento", referencedColumnName = "Id")
    @ManyToOne
    private Departamento departamento;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @OneToMany(mappedBy = "municipio")
    private Collection<Corregimiento> corregimientoCollection;

    public Municipio() {
    }

    public Municipio(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<Documento> getDocumentoCollection() {
        return documentoCollection;
    }

    public void setDocumentoCollection(Collection<Documento> documentoCollection) {
        this.documentoCollection = documentoCollection;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Departamento getDepartamento() {
        return departamento;
    }

    public void setDepartamento(Departamento departamento) {
        this.departamento = departamento;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<Corregimiento> getCorregimientoCollection() {
        return corregimientoCollection;
    }

    public void setCorregimientoCollection(Collection<Corregimiento> corregimientoCollection) {
        this.corregimientoCollection = corregimientoCollection;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Municipio)) {
            return false;
        }
        Municipio other = (Municipio) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.Municipio[ id=" + id + " ]";
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Vereda> getVeredaList() {
        return veredaList;
    }

    public void setVeredaList(List<Vereda> veredaList) {
        this.veredaList = veredaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Entidad> getEntidadList() {
        return entidadList;
    }

    public void setEntidadList(List<Entidad> entidadList) {
        this.entidadList = entidadList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Acta> getActaList() {
        return actaList;
    }

    public void setActaList(List<Acta> actaList) {
        this.actaList = actaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Informe> getInformeList() {
        return informeList;
    }

    public void setInformeList(List<Informe> informeList) {
        this.informeList = informeList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Circular> getCircularList() {
        return circularList;
    }

    public void setCircularList(List<Circular> circularList) {
        this.circularList = circularList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Certificado> getCertificadoList() {
        return certificadoList;
    }

    public void setCertificadoList(List<Certificado> certificadoList) {
        this.certificadoList = certificadoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Constancia> getConstanciaList() {
        return constanciaList;
    }

    public void setConstanciaList(List<Constancia> constanciaList) {
        this.constanciaList = constanciaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Comunicacion> getComunicacionList() {
        return comunicacionList;
    }

    public void setComunicacionList(List<Comunicacion> comunicacionList) {
        this.comunicacionList = comunicacionList;
    }

}
