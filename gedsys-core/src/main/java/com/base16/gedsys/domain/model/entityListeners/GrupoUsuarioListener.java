package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.user.GrupoUsuario;
import com.base16.gedsys.domain.model.user.Usuario;

import javax.faces.context.FacesContext;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.servlet.http.HttpSession;
import java.util.Date;

public class GrupoUsuarioListener {
    @PrePersist
    public void setCreated(GrupoUsuario g) {
        g.setFechaCreacion(new Date());
        g.setCreadoPor(getCurrentSessionUser());
    }

    @PreUpdate
    public void setUpdated(GrupoUsuario g) {
        g.setFechaModificacion(new Date());
        g.setModificadoPor(getCurrentSessionUser());
    }

    private Usuario getCurrentSessionUser() {
        HttpSession session;
        FacesContext fc = FacesContext.getCurrentInstance();
        if (fc != null) {
            session = (HttpSession) fc.getExternalContext().getSession(false);
            if (session != null) {
                return (Usuario) session.getAttribute("usuario");
            }
        }
        return null;
    }
}
