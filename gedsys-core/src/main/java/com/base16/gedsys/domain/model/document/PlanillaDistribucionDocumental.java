/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 *
 * <AUTHOR> Programacion02
 */
@Entity
@Table(name = "planillaDistribucionDocumental", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "PlanillaDistribucionDocumental.findAll", query = "SELECT p FROM PlanillaDistribucionDocumental p")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findById", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.id = :id")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findByNombre", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.nombre = :nombre")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findByEstado", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.estado = :estado")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findByFechaCreacion", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findByFechaModificacion", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findByCreadoPor", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.creadoPor = :creadoPor")
    , @NamedQuery(name = "PlanillaDistribucionDocumental.findByModificadoPor", query = "SELECT p FROM PlanillaDistribucionDocumental p WHERE p.modificadoPor = :modificadoPor")})
public class PlanillaDistribucionDocumental implements Serializable {

    @Size(max = 250)
    @Column(name = "RutaArchivo")
    private String rutaArchivo;
    @Size(max = 250)
    @Column(name = "NombreArchivo")
    private String nombreArchivo;

    @JoinColumn(name = "mensajero", referencedColumnName = "Id")
    @ManyToOne
    private Usuario mensajero;

    @JoinColumn(name = "Seccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccion;



    @OneToMany(mappedBy = "idPlanillaDistribucion")
    private List<PlanillaDistribucionXMensajeriaDocumental> planillaDistribucionXMensajeriaDocumentalList;

    @Size(max = 255)
    @Column(name = "consecutivo")
    private String consecutivo;

    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Size(max = 255)
    @Column(name = "nombre")
    private String nombre;
    @Column(name = "estado")
    private Integer estado;
    @Column(name = "fechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "fechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    private List<MensajeriaDocumental> mensajeriaDocumentalList;

    public PlanillaDistribucionDocumental() {
    }

    public PlanillaDistribucionDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @XmlTransient
    @JsonIgnore
    public List<MensajeriaDocumental> getMensajeriaDocumentalList() {
        return mensajeriaDocumentalList;
    }

    public void setMensajeriaDocumentalList(List<MensajeriaDocumental> mensajeriaDocumentalList) {
        this.mensajeriaDocumentalList = mensajeriaDocumentalList;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof PlanillaDistribucionDocumental)) {
            return false;
        }
        PlanillaDistribucionDocumental other = (PlanillaDistribucionDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.document.PlanillaDistribucionDocumental[ id=" + id + " ]";
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    @XmlTransient
    @JsonIgnore
    public List<PlanillaDistribucionXMensajeriaDocumental> getPlanillaDistribucionXMensajeriaDocumentalList() {
        return planillaDistribucionXMensajeriaDocumentalList;
    }

    public void setPlanillaDistribucionXMensajeriaDocumentalList(List<PlanillaDistribucionXMensajeriaDocumental> planillaDistribucionXMensajeriaDocumentalList) {
        this.planillaDistribucionXMensajeriaDocumentalList = planillaDistribucionXMensajeriaDocumentalList;
    }

    public SeccionSubSeccion getSeccion() {
        return seccion;
    }

    public void setSeccion(SeccionSubSeccion seccion) {
        this.seccion = seccion;
    }

    public Usuario getMensajero() {
        return mensajero;
    }

    public void setMensajero(Usuario mensajero) {
        this.mensajero = mensajero;
    }

    public String getRutaArchivo() {
        return rutaArchivo;
    }

    public void setRutaArchivo(String rutaArchivo) {
        this.rutaArchivo = rutaArchivo;
    }

    public String getNombreArchivo() {
        return nombreArchivo;
    }

    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }


    
}
