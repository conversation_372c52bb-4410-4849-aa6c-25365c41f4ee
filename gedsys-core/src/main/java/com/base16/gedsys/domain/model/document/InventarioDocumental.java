/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "inventarioDocumental", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "InventarioDocumental.findAll", query = "SELECT i FROM InventarioDocumental i")
    , @NamedQuery(name = "InventarioDocumental.findById", query = "SELECT i FROM InventarioDocumental i WHERE i.id = :id")
    , @NamedQuery(name = "InventarioDocumental.findByNumeroDeOrden", query = "SELECT i FROM InventarioDocumental i WHERE i.numeroDeOrden = :numeroDeOrden")
    , @NamedQuery(name = "InventarioDocumental.findByCodigo", query = "SELECT i FROM InventarioDocumental i WHERE i.codigo = :codigo")
    , @NamedQuery(name = "InventarioDocumental.findByAsunto", query = "SELECT i FROM InventarioDocumental i WHERE i.asunto = :asunto")
    , @NamedQuery(name = "InventarioDocumental.findByFechaInicial", query = "SELECT i FROM InventarioDocumental i WHERE i.fechaInicial = :fechaInicial")
    , @NamedQuery(name = "InventarioDocumental.findByFechaFinal", query = "SELECT i FROM InventarioDocumental i WHERE i.fechaFinal = :fechaFinal")
    , @NamedQuery(name = "InventarioDocumental.findByCaja", query = "SELECT i FROM InventarioDocumental i WHERE i.caja = :caja")
    , @NamedQuery(name = "InventarioDocumental.findByCarpeta", query = "SELECT i FROM InventarioDocumental i WHERE i.carpeta = :carpeta")
    , @NamedQuery(name = "InventarioDocumental.findByTomo", query = "SELECT i FROM InventarioDocumental i WHERE i.tomo = :tomo")
    , @NamedQuery(name = "InventarioDocumental.findByOtro", query = "SELECT i FROM InventarioDocumental i WHERE i.otro = :otro")
    , @NamedQuery(name = "InventarioDocumental.findByFolios", query = "SELECT i FROM InventarioDocumental i WHERE i.folios = :folios")
    , @NamedQuery(name = "InventarioDocumental.findBySoporte", query = "SELECT i FROM InventarioDocumental i WHERE i.soporte = :soporte")
    , @NamedQuery(name = "InventarioDocumental.findByFrecuenciaConsulta", query = "SELECT i FROM InventarioDocumental i WHERE i.frecuenciaConsulta = :frecuenciaConsulta")})
public class InventarioDocumental implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @Size(max = 250)
    @Column(name = "tipoInventario")
    private String tipoInventario;
    @Size(max = 250)
    @Column(name = "deposito")
    private String deposito;
    @Size(max = 250)
    @Column(name = "estanteria")
    private String estanteria;
    @Size(max = 250)
    @Column(name = "lado")
    private String lado;
    @Size(max = 250)
    @Column(name = "entrepano")
    private String entrepano;
    @Size(max = 250)
    @Column(name = "nombreCaja")
    private String nombreCaja;
    @Size(max = 250)
    @Column(name = "nombreCarpeta")
    private String nombreCarpeta;
    @Column(name = "caja")
    private Boolean caja;
    @Column(name = "carpeta")
    private Boolean carpeta;
    @Column(name = "tomo")
    private Boolean tomo;
    @Column(name = "otro")
    private Boolean otro;
    @Size(max = 250)
    @Column(name = "entidadRemitente")
    private String entidadRemitente;
    @Size(max = 250)
    @Column(name = "entidadProductora")
    private String entidadProductora;
    @Size(max = 250)
    @Column(name = "entidadAdministrativa")
    private String entidadAdministrativa;
    @Size(max = 250)
    @Column(name = "objeto")
    private String objeto;
    @Size(max = 250)
    @Column(name = "oficinaProductora")
    private String oficinaProductora;
    @Column(name = "hojaNumero")
    private Integer hojaNumero;
    @Column(name = "numeroTotal")
    private Integer numeroTotal;
    @Size(max = 100)
    @Column(name = "numeroDeOrden")
    private String numeroDeOrden;
    @Size(max = 100)
    @Column(name = "codigo")
    private String codigo;
    @Size(max = 250)
    @Column(name = "asunto")
    private String asunto;
    @Column(name = "fechaInicial")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaInicial;
    @Column(name = "fechaFinal")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFinal;
    @Size(max = 100)
    @Column(name = "folios")
    private String folios;
    @Size(max = 100)
    @Column(name = "soporte")
    private String soporte;
    @Size(max = 100)
    @Column(name = "frecuenciaConsulta")
    private String frecuenciaConsulta;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Observaciones")
    private String observaciones;

    public InventarioDocumental() {
    }

    public InventarioDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNumeroDeOrden() {
        return numeroDeOrden;
    }

    public void setNumeroDeOrden(String numeroDeOrden) {
        this.numeroDeOrden = numeroDeOrden;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public Date getFechaInicial() {
        return fechaInicial;
    }

    public void setFechaInicial(Date fechaInicial) {
        this.fechaInicial = fechaInicial;
    }

    public Date getFechaFinal() {
        return fechaFinal;
    }

    public void setFechaFinal(Date fechaFinal) {
        this.fechaFinal = fechaFinal;
    }

    public String getFolios() {
        return folios;
    }

    public void setFolios(String folios) {
        this.folios = folios;
    }

    public String getSoporte() {
        return soporte;
    }

    public void setSoporte(String soporte) {
        this.soporte = soporte;
    }

    public String getFrecuenciaConsulta() {
        return frecuenciaConsulta;
    }

    public void setFrecuenciaConsulta(String frecuenciaConsulta) {
        this.frecuenciaConsulta = frecuenciaConsulta;
    }

    public String getObservaciones() {
        return observaciones;
    }

    public void setObservaciones(String observaciones) {
        this.observaciones = observaciones;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof InventarioDocumental)) {
            return false;
        }
        InventarioDocumental other = (InventarioDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.document.InventarioDocumental[ id=" + id + " ]";
    }

    public Boolean getCaja() {
        return caja;
    }

    public void setCaja(Boolean caja) {
        this.caja = caja;
    }

    public Boolean getCarpeta() {
        return carpeta;
    }

    public void setCarpeta(Boolean carpeta) {
        this.carpeta = carpeta;
    }

    public Boolean getTomo() {
        return tomo;
    }

    public void setTomo(Boolean tomo) {
        this.tomo = tomo;
    }

    public Boolean getOtro() {
        return otro;
    }

    public void setOtro(Boolean otro) {
        this.otro = otro;
    }

    public String getDeposito() {
        return deposito;
    }

    public void setDeposito(String deposito) {
        this.deposito = deposito;
    }

    public String getEstanteria() {
        return estanteria;
    }

    public void setEstanteria(String estanteria) {
        this.estanteria = estanteria;
    }

    public String getLado() {
        return lado;
    }

    public void setLado(String lado) {
        this.lado = lado;
    }

    public String getEntrepano() {
        return entrepano;
    }

    public void setEntrepano(String entrepano) {
        this.entrepano = entrepano;
    }

    public String getNombreCaja() {
        return nombreCaja;
    }

    public void setNombreCaja(String nombreCaja) {
        this.nombreCaja = nombreCaja;
    }

    public String getNombreCarpeta() {
        return nombreCarpeta;
    }

    public void setNombreCarpeta(String nombreCarpeta) {
        this.nombreCarpeta = nombreCarpeta;
    }

    public String getTipoInventario() {
        return tipoInventario;
    }

    public void setTipoInventario(String tipoInventario) {
        this.tipoInventario = tipoInventario;
    }

    public String getEntidadRemitente() {
        return entidadRemitente;
    }

    public void setEntidadRemitente(String entidadRemitente) {
        this.entidadRemitente = entidadRemitente;
    }

    public String getEntidadProductora() {
        return entidadProductora;
    }

    public void setEntidadProductora(String entidadProductora) {
        this.entidadProductora = entidadProductora;
    }

    public String getEntidadAdministrativa() {
        return entidadAdministrativa;
    }

    public void setEntidadAdministrativa(String entidadAdministrativa) {
        this.entidadAdministrativa = entidadAdministrativa;
    }

    public String getOficinaProductora() {
        return oficinaProductora;
    }

    public void setOficinaProductora(String oficinaProductora) {
        this.oficinaProductora = oficinaProductora;
    }

    public Integer getHojaNumero() {
        return hojaNumero;
    }

    public void setHojaNumero(Integer hojaNumero) {
        this.hojaNumero = hojaNumero;
    }

    public Integer getNumeroTotal() {
        return numeroTotal;
    }

    public void setNumeroTotal(Integer numeroTotal) {
        this.numeroTotal = numeroTotal;
    }

    public String getObjeto() {
        return objeto;
    }

    public void setObjeto(String objeto) {
        this.objeto = objeto;
    }

}
