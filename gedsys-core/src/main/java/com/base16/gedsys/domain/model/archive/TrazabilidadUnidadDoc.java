/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "trazabilidadUnidadDoc", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "TrazabilidadUnidadDoc.findAll", query = "SELECT t FROM TrazabilidadUnidadDoc t")
    , @NamedQuery(name = "TrazabilidadUnidadDoc.findById", query = "SELECT t FROM TrazabilidadUnidadDoc t WHERE t.id = :id")
    , @NamedQuery(name = "TrazabilidadUnidadDoc.findByUnidadDoc", query = "SELECT t FROM TrazabilidadUnidadDoc t WHERE t.unidadDoc = :unidadDoc")
    , @NamedQuery(name = "TrazabilidadUnidadDoc.findByUsuario", query = "SELECT t FROM TrazabilidadUnidadDoc t WHERE t.usuario = :usuario")
    , @NamedQuery(name = "TrazabilidadUnidadDoc.findByFechaRegistro", query = "SELECT t FROM TrazabilidadUnidadDoc t WHERE t.fechaRegistro = :fechaRegistro")})
public class TrazabilidadUnidadDoc implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;

    @Column(name = "FechaRegistro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "Accion")
    private String accion;

    @JoinColumn(name = "Usuario", referencedColumnName = "Id")
    @ManyToOne
    private Usuario usuario;

    @JoinColumn(name = "UnidadDoc", referencedColumnName = "Id")
    @ManyToOne
    private UnidadDocumental unidadDoc;

    public TrazabilidadUnidadDoc() {
    }

    public TrazabilidadUnidadDoc(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public String getAccion() {
        return accion;
    }

    public void setAccion(String accion) {
        this.accion = accion;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public UnidadDocumental getUnidadDoc() {
        return unidadDoc;
    }

    public void setUnidadDoc(UnidadDocumental unidadDoc) {
        this.unidadDoc = unidadDoc;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof TrazabilidadUnidadDoc)) {
            return false;
        }
        TrazabilidadUnidadDoc other = (TrazabilidadUnidadDoc) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.archive.TrazabilidadUnidadDoc[ id=" + id + " ]";
    }

}
