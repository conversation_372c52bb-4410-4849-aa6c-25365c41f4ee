/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.list;

import com.base16.gedsys.domain.model.list.exceptions.NonexistentEntityException;
import java.io.Serializable;
import java.util.List;
import javax.persistence.EntityManager;

/**
 *
 * <AUTHOR> Programacion02
 */
public interface ListasJpaControllerInterface extends Serializable {

    void create(Listas listas);

    void destroy(Integer id) throws NonexistentEntityException;

    void edit(Listas listas) throws NonexistentEntityException, Exception;

    Listas findListas(Integer id);

    List<Listas> findListasEntities();
    
    List<Listas> buscar(Listas lista);

    EntityManager getEntityManager();

    int getListasCount();
    
    List<Listas> findListasEntitiesUniqueLista();
    
}
