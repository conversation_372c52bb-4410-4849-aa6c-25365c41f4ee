/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.entityListeners.ConsecutivosUsuarioListener;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "consecutivosUsuario", schema = "dbo")
@EntityListeners(ConsecutivosUsuarioListener.class)
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "ConsecutivosUsuario.findAll", query = "SELECT c FROM ConsecutivosUsuario c")
        , @NamedQuery(name = "ConsecutivosUsuario.findById", query = "SELECT c FROM ConsecutivosUsuario c WHERE c.id = :id")
        , @NamedQuery(name = "ConsecutivosUsuario.findByConsecutivo", query = "SELECT c FROM ConsecutivosUsuario c WHERE c.consecutivo = :consecutivo")
        , @NamedQuery(name = "ConsecutivosUsuario.findByTipo", query = "SELECT c FROM ConsecutivosUsuario c WHERE c.tipo = :tipo")
        , @NamedQuery(name = "ConsecutivosUsuario.findByConsecutivoAndTipo", query = "SELECT c FROM ConsecutivosUsuario c WHERE c.consecutivo = :consecutivo AND c.tipo = :tipo")
        , @NamedQuery(name = "ConsecutivosUsuario.findByFechaCreacion", query = "SELECT c FROM ConsecutivosUsuario c WHERE c.fechaCreacion = :fechaCreacion")})
public class ConsecutivosUsuario implements Serializable {

    @OneToMany(mappedBy = "consecutivoUsuario")
    private List<ComentarioBusquedaRadicado> comentarioBusquedaRadicadoList;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Column(name = "Tipo")
    private String tipo;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "code")
    private String code;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @Column(name = "Activo")
    private Boolean activo;
    @JoinColumn(name = "IdDocumento", referencedColumnName = "Id")
    @ManyToOne
    private Documento idDocumento;
    @JoinColumn(name = "incidenciaGeneral", referencedColumnName = "Id")
    @ManyToOne
    private IncidenciaGeneral incidenciaGeneral;

    public ConsecutivosUsuario() {
    }

    public ConsecutivosUsuario(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Documento getIdDocumento() {
        return idDocumento;
    }

    public void setIdDocumento(Documento idDocumento) {
        this.idDocumento = idDocumento;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof ConsecutivosUsuario)) {
            return false;
        }
        ConsecutivosUsuario other = (ConsecutivosUsuario) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.ConsecutivosUsuario[ id=" + id + " ]";
    }

    public Boolean getActivo() {
        return activo;
    }

    public void setActivo(Boolean activo) {
        this.activo = activo;
    }

    @XmlTransient
    @JsonIgnore
    public List<ComentarioBusquedaRadicado> getComentarioBusquedaRadicadoList() {
        return comentarioBusquedaRadicadoList;
    }

    public void setComentarioBusquedaRadicadoList(List<ComentarioBusquedaRadicado> comentarioBusquedaRadicadoList) {
        this.comentarioBusquedaRadicadoList = comentarioBusquedaRadicadoList;
    }

    public IncidenciaGeneral getIncidenciaGeneral() {
        return incidenciaGeneral;
    }

    public void setIncidenciaGeneral(IncidenciaGeneral incidenciaGeneral) {
        this.incidenciaGeneral = incidenciaGeneral;
    }
}
