/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.user.Usuario;
import com.base16.gedsys.domain.model.archive.SubSerie;
import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.archive.Serie;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.location.Municipio;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "informe", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Informe.findAll", query = "SELECT i FROM Informe i")
    , @NamedQuery(name = "Informe.findById", query = "SELECT i FROM Informe i WHERE i.id = :id")
    , @NamedQuery(name = "Informe.findByDocumentoFirmado", query = "SELECT i FROM Informe i WHERE i.documentoFirmado = :documentoFirmado")
    , @NamedQuery(name = "Informe.findByFechaCreacion", query = "SELECT i FROM Informe i WHERE i.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Informe.findByFechaModificacion", query = "SELECT i FROM Informe i WHERE i.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Informe.findByFechaFirma", query = "SELECT i FROM Informe i WHERE i.fechaFirma = :fechaFirma")
    , @NamedQuery(name = "Informe.findByEstadoYUsuario", query = "SELECT i FROM Informe i WHERE i.estado = :estado AND i.remitente = :usuario")
    , @NamedQuery(name = "Informe.findByEstado", query = "SELECT i FROM Informe i WHERE i.estado = :estado")
    , @NamedQuery(name = "Informe.findByAsunto", query = "SELECT i FROM Informe i WHERE i.asunto = :asunto")})
public class Informe implements Serializable {

    @Column(name = "active")
    private Boolean active;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "asunto")
    private String asunto;

    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;

    @OneToMany(mappedBy = "informe")
    private List<InformeRemInterno> informeRemInternoList;

    @Column(name = "isExtern")
    private Boolean isExtern;

    @Column(name = "fechaDocumento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaDocumento;

    @JoinColumn(name = "ciudad", referencedColumnName = "Id")
    @ManyToOne
    private Municipio ciudad;

    @Size(max = 255)
    @Column(name = "CargoFirmaUno")
    private String cargoFirmaUno;

    @Lob
    @Column(name = "comentario")
    private String comentario;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "ConCopiaExterna")
    private String conCopiaExterna;

    @JoinColumn(name = "quienProyecto", referencedColumnName = "Id")
    @ManyToOne
    private Usuario quienProyecto;

    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;

    @Column(name = "Copia")
    private Boolean copia;
    @JoinColumn(name = "SeccionSubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccionSubSeccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "informe")
    private Collection<InformeConCopia> informeccCollection;

    @Column(name = "Anexos")
    private String anexos;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Lob
    @Column(name = "Objetivo")
    private String objetivo;
    @Lob
    @Column(name = "Conclusiones")
    private String conclusiones;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "FechaFirma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;
    @Size(max = 50)
    @Column(name = "Estado")
    private String estado;
    @Column(name = "TipoComunicacion")
    private String tipoComunicacion;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "informe")
    private List<InformeConCopia> informeccList;
    @JoinColumn(name = "Remitente", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario remitente;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario modificadoPor;

    @JoinColumn(name = "DocumentoFirmado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoFirmado;

    public Informe() {
    }

    public Informe(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    public String getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(String objetivo) {
        this.objetivo = objetivo;
    }

    public String getConclusiones() {
        return conclusiones;
    }

    public void setConclusiones(String conclusiones) {
        this.conclusiones = conclusiones;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public Usuario getRemitente() {
        return remitente;
    }

    public void setRemitente(Usuario remitente) {
        this.remitente = remitente;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public String getTipoComunicacion() {
        return tipoComunicacion;
    }

    public void setTipoComunicacion(String tipoComunicacion) {
        this.tipoComunicacion = tipoComunicacion;
    }

    @XmlTransient
    @JsonIgnore
    public List<InformeConCopia> getInformeccList() {
        return informeccList;
    }

    public void setInformeccList(List<InformeConCopia> informeccList) {
        this.informeccList = informeccList;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Informe)) {
            return false;
        }
        Informe other = (Informe) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Informe[ id=" + id + " ]";
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<InformeConCopia> getInformeccCollection() {
        return informeccCollection;
    }

    public void setInformeccCollection(Collection<InformeConCopia> informeccCollection) {
        this.informeccCollection = informeccCollection;
    }

    public Boolean getCopia() {
        return copia;
    }

    public void setCopia(Boolean copia) {
        this.copia = copia;
    }

    public SeccionSubSeccion getSeccionSubSeccion() {
        return seccionSubSeccion;
    }

    public void setSeccionSubSeccion(SeccionSubSeccion seccionSubSeccion) {
        this.seccionSubSeccion = seccionSubSeccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    public Usuario getQuienProyecto() {
        return quienProyecto;
    }

    public void setQuienProyecto(Usuario quienProyecto) {
        this.quienProyecto = quienProyecto;
    }

    public String getConCopiaExterna() {
        return conCopiaExterna;
    }

    public void setConCopiaExterna(String conCopiaExterna) {
        this.conCopiaExterna = conCopiaExterna;
    }

    public String getCargoFirmaUno() {
        return cargoFirmaUno;
    }

    public void setCargoFirmaUno(String cargoFirmaUno) {
        this.cargoFirmaUno = cargoFirmaUno;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Municipio getCiudad() {
        return ciudad;
    }

    public void setCiudad(Municipio ciudad) {
        this.ciudad = ciudad;
    }

    public Date getFechaDocumento() {
        return fechaDocumento;
    }

    public void setFechaDocumento(Date fechaDocumento) {
        this.fechaDocumento = fechaDocumento;
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    @XmlTransient
    @JsonIgnore
    public List<InformeRemInterno> getInformeRemInternoList() {
        return informeRemInternoList;
    }

    public void setInformeRemInternoList(List<InformeRemInterno> informeRemInternoList) {
        this.informeRemInternoList = informeRemInternoList;
    }

    public Documento getDocumentoFirmado() {
        return documentoFirmado;
    }

    public void setDocumentoFirmado(Documento documentoFirmado) {
        this.documentoFirmado = documentoFirmado;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
