/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.archive.Serie;
import com.base16.gedsys.domain.model.archive.SubSerie;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.document.Entidad;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "resolucion", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Resolucion.findAll", query = "SELECT r FROM Resolucion r")
    , @NamedQuery(name = "Resolucion.findById", query = "SELECT r FROM Resolucion r WHERE r.id = :id")
    , @NamedQuery(name = "Resolucion.findByTratamiento", query = "SELECT r FROM Resolucion r WHERE r.tratamiento = :tratamiento")
    , @NamedQuery(name = "Resolucion.findByDestinatario", query = "SELECT r FROM Resolucion r WHERE r.destinatario = :destinatario")
    , @NamedQuery(name = "Resolucion.findByCargo", query = "SELECT r FROM Resolucion r WHERE r.cargo = :cargo")
    , @NamedQuery(name = "Resolucion.findByFechaCreacion", query = "SELECT r FROM Resolucion r WHERE r.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Resolucion.findByFechaModificacion", query = "SELECT r FROM Resolucion r WHERE r.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Resolucion.findByFechaFirma", query = "SELECT r FROM Resolucion r WHERE r.fechaFirma = :fechaFirma")
    , @NamedQuery(name = "Resolucion.findByEstado", query = "SELECT r FROM Resolucion r WHERE r.estado = :estado")
    , @NamedQuery(name = "Resolucion.findByDireccion", query = "SELECT r FROM Resolucion r WHERE r.direccion = :direccion")
    , @NamedQuery(name = "Resolucion.findByCiudad", query = "SELECT r FROM Resolucion r WHERE r.ciudad = :ciudad")
    , @NamedQuery(name = "Resolucion.findByDocumentoPadre", query = "SELECT r FROM Resolucion r WHERE r.documentoPadre = :documentoPadre")
    , @NamedQuery(name = "Resolucion.findByAnexos", query = "SELECT r FROM Resolucion r WHERE r.anexos = :anexos")
    , @NamedQuery(name = "Resolucion.findByTipoComunicacion", query = "SELECT r FROM Resolucion r WHERE r.tipoComunicacion = :tipoComunicacion")
    , @NamedQuery(name = "Resolucion.findByCopia", query = "SELECT r FROM Resolucion r WHERE r.copia = :copia")
    , @NamedQuery(name = "Resolucion.findByMediorecepcion", query = "SELECT r FROM Resolucion r WHERE r.mediorecepcion = :mediorecepcion")
    , @NamedQuery(name = "Resolucion.findByCargoFirmaUno", query = "SELECT r FROM Resolucion r WHERE r.cargoFirmaUno = :cargoFirmaUno")
    , @NamedQuery(name = "Resolucion.findByCargoFirmaDos", query = "SELECT r FROM Resolucion r WHERE r.cargoFirmaDos = :cargoFirmaDos")
    , @NamedQuery(name = "Resolucion.findByComentario", query = "SELECT r FROM Resolucion r WHERE r.comentario = :comentario")
    , @NamedQuery(name = "Resolucion.findByIsExtern", query = "SELECT r FROM Resolucion r WHERE r.isExtern = :isExtern")
    , @NamedQuery(name = "Resolucion.findByDias", query = "SELECT r FROM Resolucion r WHERE r.dias = :dias")
    , @NamedQuery(name = "Resolucion.findByFechaFirmaRem1", query = "SELECT r FROM Resolucion r WHERE r.fechaFirmaRem1 = :fechaFirmaRem1")
    , @NamedQuery(name = "Resolucion.findByFechaFirmaRem2", query = "SELECT r FROM Resolucion r WHERE r.fechaFirmaRem2 = :fechaFirmaRem2")})
public class Resolucion implements Serializable {

    @Column(name = "active")
    private Boolean active;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Size(max = 255)
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Size(max = 45)
    @Column(name = "Tratamiento")
    private String tratamiento;
    @Size(max = 255)
    @Column(name = "Destinatario")
    private String destinatario;
    @Size(max = 255)
    @Column(name = "Cargo")
    private String cargo;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Asunto")
    private String asunto;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Contenido")
    private String contenido;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Despedida")
    private String despedida;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "FechaAprobacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaAprobacion;
    @Column(name = "FechaFirma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;
    @Size(max = 50)
    @Column(name = "Estado")
    private String estado;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Empresa")
    private String empresa;
    @Size(max = 100)
    @Column(name = "Direccion")
    private String direccion;
    @Column(name = "Ciudad")
    private Integer ciudad;
    @Column(name = "DocumentoPadre")
    private Integer documentoPadre;
    @Size(max = 255)
    @Column(name = "Anexos")
    private String anexos;
    @Size(max = 50)
    @Column(name = "TipoComunicacion")
    private String tipoComunicacion;
    @Column(name = "Copia")
    private Boolean copia;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "concopiaExterna")
    private String concopiaExterna;
    @Column(name = "mediorecepcion")
    private Integer mediorecepcion;
    @Size(max = 255)
    @Column(name = "cargoFirmaUno")
    private String cargoFirmaUno;
    @Size(max = 255)
    @Column(name = "cargoFirmaDos")
    private String cargoFirmaDos;
    @Size(max = 2147483647)
    @Column(name = "comentario")
    private String comentario;
    @Column(name = "isExtern")
    private Boolean isExtern;
    // @Pattern(regexp="[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?", message="Invalid email")//if the field contains email address consider using this annotation to enforce field validation
    @Lob
    @Size(max = 2147483647)
    @Column(name = "email")
    private String email;
    @Column(name = "Dias")
    private Integer dias;
    @Column(name = "fechaFirmaRem1")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaRem1;
    @Column(name = "fechaFirmaRem2")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaRem2;

    @Column(name = "formatos_id")
    private Integer formatoId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "formatos_id", referencedColumnName = "Id", insertable = false, updatable = false)
    private Formatos formato;

    @JoinColumn(name = "entidad", referencedColumnName = "id")
    @ManyToOne
    private Entidad entidad;
    @JoinColumn(name = "SeccionSubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccionSubSeccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;
    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;
    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;
    @JoinColumn(name = "Remitente2", referencedColumnName = "Id")
    @ManyToOne
    private Usuario remitente2;
    @JoinColumn(name = "quienProyecto", referencedColumnName = "Id")
    @ManyToOne
    private Usuario quienProyecto;
    @JoinColumn(name = "Remitente", referencedColumnName = "Id")
    @ManyToOne
    private Usuario remitente;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @JoinColumn(name = "revisoyaprobo", referencedColumnName = "Id")
    @ManyToOne
    private Usuario revisoyaprobo;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;

    @JoinColumn(name = "DocumentoFirmado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoFirmado;

    public Resolucion() {
    }

    public Resolucion(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    public String getTratamiento() {
        return tratamiento;
    }

    public void setTratamiento(String tratamiento) {
        this.tratamiento = tratamiento;
    }

    public String getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(String destinatario) {
        this.destinatario = destinatario;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getContenido() {
        return contenido;
    }

    public void setContenido(String contenido) {
        this.contenido = contenido;
    }

    public String getDespedida() {
        return despedida;
    }

    public void setDespedida(String despedida) {
        this.despedida = despedida;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getDireccion() {
        return direccion;
    }

    public void setDireccion(String direccion) {
        this.direccion = direccion;
    }

    public Integer getCiudad() {
        return ciudad;
    }

    public void setCiudad(Integer ciudad) {
        this.ciudad = ciudad;
    }

    public Integer getDocumentoPadre() {
        return documentoPadre;
    }

    public void setDocumentoPadre(Integer documentoPadre) {
        this.documentoPadre = documentoPadre;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    public String getTipoComunicacion() {
        return tipoComunicacion;
    }

    public void setTipoComunicacion(String tipoComunicacion) {
        this.tipoComunicacion = tipoComunicacion;
    }

    public Boolean getCopia() {
        return copia;
    }

    public void setCopia(Boolean copia) {
        this.copia = copia;
    }

    public String getConcopiaExterna() {
        return concopiaExterna;
    }

    public void setConcopiaExterna(String concopiaExterna) {
        this.concopiaExterna = concopiaExterna;
    }

    public Integer getMediorecepcion() {
        return mediorecepcion;
    }

    public void setMediorecepcion(Integer mediorecepcion) {
        this.mediorecepcion = mediorecepcion;
    }

    public String getCargoFirmaUno() {
        return cargoFirmaUno;
    }

    public void setCargoFirmaUno(String cargoFirmaUno) {
        this.cargoFirmaUno = cargoFirmaUno;
    }

    public String getCargoFirmaDos() {
        return cargoFirmaDos;
    }

    public void setCargoFirmaDos(String cargoFirmaDos) {
        this.cargoFirmaDos = cargoFirmaDos;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public Date getFechaFirmaRem1() {
        return fechaFirmaRem1;
    }

    public void setFechaFirmaRem1(Date fechaFirmaRem1) {
        this.fechaFirmaRem1 = fechaFirmaRem1;
    }

    public Date getFechaFirmaRem2() {
        return fechaFirmaRem2;
    }

    public void setFechaFirmaRem2(Date fechaFirmaRem2) {
        this.fechaFirmaRem2 = fechaFirmaRem2;
    }

    public Entidad getEntidad() {
        return entidad;
    }

    public void setEntidad(Entidad entidad) {
        this.entidad = entidad;
    }

    public SeccionSubSeccion getSeccionSubSeccion() {
        return seccionSubSeccion;
    }

    public void setSeccionSubSeccion(SeccionSubSeccion seccionSubSeccion) {
        this.seccionSubSeccion = seccionSubSeccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    public Usuario getRemitente2() {
        return remitente2;
    }

    public void setRemitente2(Usuario remitente2) {
        this.remitente2 = remitente2;
    }

    public Usuario getQuienProyecto() {
        return quienProyecto;
    }

    public void setQuienProyecto(Usuario quienProyecto) {
        this.quienProyecto = quienProyecto;
    }

    public Usuario getRemitente() {
        return remitente;
    }

    public void setRemitente(Usuario remitente) {
        this.remitente = remitente;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Usuario getRevisoyaprobo() {
        return revisoyaprobo;
    }

    public void setRevisoyaprobo(Usuario revisoyaprobo) {
        this.revisoyaprobo = revisoyaprobo;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Resolucion)) {
            return false;
        }
        Resolucion other = (Resolucion) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.documentProducer.Resolucion[ id=" + id + " ]";
    }


    public Date getFechaAprobacion() {
        return fechaAprobacion;
    }

    public void setFechaAprobacion(Date fechaAprobacion) {
        this.fechaAprobacion = fechaAprobacion;
    }


    public Documento getDocumentoFirmado() {
        return documentoFirmado;
    }

    public void setDocumentoFirmado(Documento documentoFirmado) {
        this.documentoFirmado = documentoFirmado;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Integer getFormatoId() {
        return formatoId;
    }

    public void setFormatoId(Integer formatoId) {
        this.formatoId = formatoId;
    }

    public Formatos getFormato() {
        return formato;
    }

    public void setFormato(Formatos formato) {
        this.formato = formato;
    }
}
