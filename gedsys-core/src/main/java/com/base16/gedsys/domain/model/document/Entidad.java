/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.documentProducer.Resolucion;
import com.base16.gedsys.domain.model.entityListeners.EntidadListener;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "entidad", schema = "dbo")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@EntityListeners(EntidadListener.class)
@NamedQueries({
        @NamedQuery(name = "Entidad.findAll", query = "SELECT e FROM Entidad e ORDER BY e.nombre ASC")
        , @NamedQuery(name = "Entidad.findListEntidadTemp", query = "SELECT e FROM Entidad e WHERE e.temporal = :estado or e.temporal is null ORDER BY e.nombre ASC")
        , @NamedQuery(name = "Entidad.findById", query = "SELECT e FROM Entidad e WHERE e.id = :id")
        , @NamedQuery(name = "Entidad.findByDireccion", query = "SELECT e FROM Entidad e WHERE e.direccion = :direccion")
        , @NamedQuery(name = "Entidad.findByEmail", query = "SELECT e FROM Entidad e WHERE e.email = :email")
        , @NamedQuery(name = "Entidad.findByFechaCreacion", query = "SELECT e FROM Entidad e WHERE e.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "Entidad.findByFechaModificacion", query = "SELECT e FROM Entidad e WHERE e.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "Entidad.findByNombre", query = "SELECT e FROM Entidad e WHERE e.nombre = :nombre")
        , @NamedQuery(name = "Entidad.findByNumeroDocumento", query = "SELECT e FROM Entidad e WHERE e.numeroDocumento = :numeroDocumento")
        , @NamedQuery(name = "Entidad.findByNumeroDocument", query = "SELECT e FROM Entidad e WHERE e.numeroDocumento = :num")
        , @NamedQuery(name = "Entidad.findByTelefono", query = "SELECT e FROM Entidad e WHERE e.telefono = :telefono")
        , @NamedQuery(name = "Entidad.findByTipoDocumento", query = "SELECT e FROM Entidad e WHERE e.tipoDocumento = :tipoDocumento")
        , @NamedQuery(name = "Entidad.buscarEntidadPermanente", query = "SELECT e FROM Entidad e WHERE e.tipoDocumento = :tipoDocumento AND e.numeroDocumento = :numeroIdentificacion AND e.temporal = :estado")
})
public class Entidad implements Serializable {

    @OneToMany(mappedBy = "entidad")
    private List<Resolucion> resolucionList;

    @JoinColumn(name = "municipio", referencedColumnName = "Id")
    @ManyToOne
    private Municipio municipio;

    @Size(max = 15)
    @Column(name = "CodigoPostal")
    private String codigoPostal;

    @OneToMany(mappedBy = "entidad")
    private List<Carta> cartaList;

    @OneToMany(mappedBy = "entidad", fetch = FetchType.LAZY)
    private List<IncidenciaGeneral> incidenciaGeneralList;

    @Column(name = "Tratamiento")
    private String tratamiento;
    @Column(name = "Cargo")
    private String cargo;
    @Column(name = "deleted")
    private Boolean deleted;

    @Column(name = "Contacto")
    private String contacto;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @Column(name = "Direccion")
    private String direccion;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "Email")
    private String email;

    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "NumeroDocumento")
    private String numeroDocumento;
    @Column(name = "Telefono")
    private String telefono;
    @Column(name = "TipoDocumento")
    private String tipoDocumento;
    @Column(name = "birthday")
    private Date birthday;
    @Column(name = "genero")
    private String genero;
    @Column(name = "temporal")
    private Boolean temporal;


    @XmlTransient
    @JsonIgnore
    @OneToMany(mappedBy = "entidad")
    private Collection<Documento> documentoCollection;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    public Entidad() {
    }

    public Entidad(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDireccion() {
        return direccion;
    }

    public void setDireccion(String direccion) {
        this.direccion = direccion;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getTelefono() {
        return telefono;
    }

    public void setTelefono(String telefono) {
        this.telefono = telefono;
    }

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<Documento> getDocumentoCollection() {
        return documentoCollection;
    }

    public void setDocumentoCollection(Collection<Documento> documentoCollection) {
        this.documentoCollection = documentoCollection;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Entidad)) {
            return false;
        }
        Entidad other = (Entidad) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.Entidad[ id=" + id + " ]";
    }

    public String getContacto() {
        return contacto;
    }

    public void setContacto(String contacto) {
        this.contacto = contacto;
    }

    public String getTratamiento() {
        return tratamiento;
    }

    public void setTratamiento(String tratamiento) {
        this.tratamiento = tratamiento;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    @XmlTransient
    @JsonIgnore
    public List<IncidenciaGeneral> getIncidenciaGeneralList() {
        return incidenciaGeneralList;
    }

    public void setIncidenciaGeneralList(List<IncidenciaGeneral> incidenciaGeneralList) {
        this.incidenciaGeneralList = incidenciaGeneralList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    public String getCodigoPostal() {
        return codigoPostal;
    }

    public void setCodigoPostal(String codigoPostal) {
        this.codigoPostal = codigoPostal;
    }

    public Municipio getMunicipio() {
        return municipio;
    }

    public void setMunicipio(Municipio municipio) {
        this.municipio = municipio;
    }

    @XmlTransient
    @JsonIgnore
    public List<Resolucion> getResolucionList() {
        return resolucionList;
    }

    public void setResolucionList(List<Resolucion> resolucionList) {
        this.resolucionList = resolucionList;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public Boolean getTemporal() {
        return temporal;
    }

    public void setTemporal(Boolean temporal) {
        this.temporal = temporal;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }
}
