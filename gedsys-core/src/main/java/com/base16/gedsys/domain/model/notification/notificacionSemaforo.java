package com.base16.gedsys.domain.model.notification;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.infrastructure.persistence.jpa.DocumentoJpaRepository;
import com.base16.gedsys.application.util.JpaUtils;
import java.util.Calendar;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import javax.persistence.EntityManagerFactory;

public class notificacionSemaforo extends Thread {

    public void run() {
        DocumentoJpaRepository jpaDoc;
        try {
            //EntityManagerFactory emf = JpaUtils.getEntityManagerFactory(this.getConfigFilePath());
            //jpaDoc = new DocumentoJpaRepository(emf);
            Timer temporizador = new Timer();

            TimerTask task = new TimerTask() {
                @Override
                public void run() {
                    System.out.print("Esta por vencer tu documento");
                    temporizador.cancel();
                }
            };

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date()); //tuFechaBase es un Date;
            calendar.add(Calendar.DAY_OF_YEAR, -5); //minutosASumar es int.
            Date fechaSalida = calendar.getTime();

            temporizador.schedule(task, fechaSalida, 10000000);
        } catch (Exception e) {
            throw e;
        }
    }
    
 
}
