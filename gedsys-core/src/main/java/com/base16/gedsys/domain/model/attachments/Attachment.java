package com.base16.gedsys.domain.model.attachments;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "Attachments")
public class Attachment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "fileName")
    private String fileName;

    @Column(name = "hash")
    private String hash;

    @Column(name = "format", length = 10)
    private String format;

    @Column(name = "remoteResource")
    private Boolean recursoRemoto;

    @Column(name = "tokenToDownload")
    private String tokenToDownload;

    @Column(name = "description")
    private String descripcion;

    @OneToMany(mappedBy = "attachment", cascade = {CascadeType.PERSIST, CascadeType.MERGE},
            fetch = FetchType.LAZY)
    private List<TraceabilityAttachment> trazabilidadList;


    public String getTokenToDownload() {
        return tokenToDownload;
    }

    public void setTokenToDownload(String tokenToDownload) {
        this.tokenToDownload = tokenToDownload;
    }

    public Boolean getRecursoRemoto() {
        return recursoRemoto;
    }

    public void setRecursoRemoto(Boolean remoteResource) {
        this.recursoRemoto = remoteResource;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public List<TraceabilityAttachment> getTrazabilidadList() {
        return trazabilidadList;
    }

    public void setTrazabilidadList(List<TraceabilityAttachment> trazabilidadList) {
        this.trazabilidadList = trazabilidadList;
    }
}