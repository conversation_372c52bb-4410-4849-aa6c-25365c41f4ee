/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.document.TransferenciaDocumental;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "unidaddocumental", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
        @NamedQuery(name = "UnidadDocumental.findAll", query = "SELECT u FROM UnidadDocumental u")
        , @NamedQuery(name = "UnidadDocumental.findById", query = "SELECT u FROM UnidadDocumental u WHERE u.id = :id")
        , @NamedQuery(name = "UnidadDocumental.findByBorrado", query = "SELECT u FROM UnidadDocumental u WHERE u.borrado = :borrado")
        , @NamedQuery(name = "UnidadDocumental.findByFechaCreacion", query = "SELECT u FROM UnidadDocumental u WHERE u.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "UnidadDocumental.findByFechaModificacion", query = "SELECT u FROM UnidadDocumental u WHERE u.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "UnidadDocumental.findBySubSerie", query = "SELECT u FROM UnidadDocumental u WHERE u.subSerie = :subSerie")
        , @NamedQuery(name = "UnidadDocumental.findByNombre", query = "SELECT u FROM UnidadDocumental u WHERE u.nombre = :nombre")
        , @NamedQuery(name = "UnidadDocumental.findByNombreUnidadDoc", query = "SELECT u FROM UnidadDocumental u WHERE u.subSeccion = :seccion and u.subSerie.serie =:serie and u.subSerie =:subserie")
        , @NamedQuery(name = "UnidadDocumental.findBySeccion", query = "SELECT u FROM UnidadDocumental u inner join u.subSeccion sub WHERE sub.id =:seccion")})

public class UnidadDocumental implements Serializable {

    @OneToMany(mappedBy = "unidadDocumental")
    private List<TransferenciaDocumental> transferenciaDocumentalList;

    @Size(max = 250)
    @Column(name = "DocumentoIdentidad")
    private String documentoIdentidad;

    private static final long serialVersionUID = 1L;
    @OneToMany(mappedBy = "unidadDocumental")
    private List<Documento> documentoList;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "Codigo")
    private String codigo;
    @Column(name = "Estado")
    private Boolean estado;
    @JoinColumn(name = "SubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion subSeccion;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @Column(name = "etiqueta")
    private String etiqueta;
    @Column(name = "deleted")
    private Boolean deleted;

    @ManyToMany(cascade = {
            CascadeType.PERSIST,
            CascadeType.MERGE
    })
    @JoinTable(
            name = "UnidadDocumentalUser",
            joinColumns = @JoinColumn(name = "UnidadDocId"),
            inverseJoinColumns = @JoinColumn(name = "UserId")
    )
    private List<Usuario> UsuariosAcceso = new ArrayList<>();
  
    public UnidadDocumental() {
    }

    public UnidadDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof UnidadDocumental)) {
            return false;
        }
        UnidadDocumental other = (UnidadDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        List<String> etiquetas = new ArrayList<>();
        if(this.etiqueta != null) {
            etiquetas = Arrays.asList(this.etiqueta.split(";"));
            return this.nombre + " " + etiquetas.toString();
        }
        return this.nombre;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Boolean getEstado() {
        return estado;
    }

    public void setEstado(Boolean estado) {
        this.estado = estado;
    }

    public SeccionSubSeccion getSubSeccion() {
        return subSeccion;
    }

    public void setSubSeccion(SeccionSubSeccion subSeccion) {
        this.subSeccion = subSeccion;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @XmlTransient
    @JsonIgnore
    public List<Documento> getDocumentoList() {
        return documentoList;
    }

    public void setDocumentoList(List<Documento> documentoList) {
        this.documentoList = documentoList;
    }

    public List<Usuario> getUsuariosAcceso() {
        return UsuariosAcceso;
    }

    public void setUsuariosAcceso(List<Usuario> UsuariosAcceso) {
        this.UsuariosAcceso = UsuariosAcceso;
    }

    public String getEtiqueta() {
        return etiqueta;
    }

    public void setEtiqueta(String etiqueta) {
        this.etiqueta = etiqueta;
    }

    public String getDocumentoIdentidad() {
        return documentoIdentidad;
    }

    public void setDocumentoIdentidad(String documentoIdentidad) {
        this.documentoIdentidad = documentoIdentidad;
    }

    @XmlTransient
    @JsonIgnore
    public List<TransferenciaDocumental> getTransferenciaDocumentalList() {
        return transferenciaDocumentalList;
    }

    public void setTransferenciaDocumentalList(List<TransferenciaDocumental> transferenciaDocumentalList) {
        this.transferenciaDocumentalList = transferenciaDocumentalList;
    }
}
