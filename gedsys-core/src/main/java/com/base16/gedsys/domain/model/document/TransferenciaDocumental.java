/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.archive.UnidadDocumental;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR> Programacion02
 */
@Entity
@Table(name = "transferenciaDocumental", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "TransferenciaDocumental.findAll", query = "SELECT t FROM TransferenciaDocumental t")
    , @NamedQuery(name = "TransferenciaDocumental.findById", query = "SELECT t FROM TransferenciaDocumental t WHERE t.id = :id")
    , @NamedQuery(name = "TransferenciaDocumental.findByFechaCreacion", query = "SELECT t FROM TransferenciaDocumental t WHERE t.fechaCreacion = :fechaCreacion")})
public class TransferenciaDocumental implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)

    @Column(name = "id")
    private Integer id;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "comentario")
    private String comentario;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "ofsignature")
    private String ofsignature;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "forsignature")
    private String forsignature;
    @Column(name = "fechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @JoinColumn(name = "unidadDocumental", referencedColumnName = "Id")
    @ManyToOne
    private UnidadDocumental unidadDocumental;

    public TransferenciaDocumental() {
    }

    public TransferenciaDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public String getOfsignature() {
        return ofsignature;
    }

    public void setOfsignature(String ofsignature) {
        this.ofsignature = ofsignature;
    }

    public String getForsignature() {
        return forsignature;
    }

    public void setForsignature(String forsignature) {
        this.forsignature = forsignature;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public UnidadDocumental getUnidadDocumental() {
        return unidadDocumental;
    }

    public void setUnidadDocumental(UnidadDocumental unidadDocumental) {
        this.unidadDocumental = unidadDocumental;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof TransferenciaDocumental)) {
            return false;
        }
        TransferenciaDocumental other = (TransferenciaDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.document.TransferenciaDocumental[ id=" + id + " ]";
    }
    
}
