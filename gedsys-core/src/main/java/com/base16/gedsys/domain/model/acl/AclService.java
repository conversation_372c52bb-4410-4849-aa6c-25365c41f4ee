/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.acl;

import com.base16.gedsys.application.Modulo;
import com.base16.gedsys.domain.model.acl.Acl;
import com.base16.gedsys.domain.model.user.Grupo;
import com.base16.gedsys.domain.model.user.Usuario;
import com.base16.gedsys.domain.model.exceptions.NonexistentEntityException;
import java.io.Serializable;
import java.util.List;
import javax.persistence.EntityManager;

/**
 *
 * <AUTHOR>
 */
public interface AclService extends Serializable {

    void create(Acl acl);

    void destroy(Integer id) throws NonexistentEntityException;

    void edit(Acl acl) throws NonexistentEntityException, Exception;

    Acl findAcl(Integer id);

    List<Acl> findAclByGrupo(Grupo grupo);

    List<Acl> findAclByUser(Usuario usuarioAcceso);

    List<Acl> findAclEntities();

    List<Acl> findAclEntities(int maxResults, int firstResult);

    List<Acl> getAclByGrupdAndModule(Grupo grupo, Modulo module);

    List<Acl> getAclByGrupdAndModule(Grupo grupo, Modulo module, int maxResults, int firstResult);

    List<Acl> getAclByUserAndModule(Usuario usuario, Modulo module);

    List<Acl> getAclByUserAndModule(Usuario usuario, Modulo module, int maxResults, int firstResult);

    int getAclCount();

    EntityManager getEntityManager();
    
}
