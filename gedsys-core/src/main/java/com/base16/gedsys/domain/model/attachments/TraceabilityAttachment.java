package com.base16.gedsys.domain.model.attachments;

import com.base16.gedsys.domain.model.entityListeners.TraceabilityAttachmentsListener;
import com.base16.gedsys.domain.model.user.Usuario;

import javax.persistence.*;
import java.util.Date;

@Entity
@EntityListeners(TraceabilityAttachmentsListener.class)
@Table(name = "TrazabilidadAttachments")
public class TraceabilityAttachment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "attachment_id", nullable = false)
    private Attachment attachment;

    @Column(name = "timestamp", nullable = false)
    private Date timestamp;

    @Column(name = "accion", nullable = false)
    private String accion;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id")
    private Usuario usuario;

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getAccion() {
        return accion;
    }

    public void setAccion(String accion) {
        this.accion = accion;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Attachment getAttachment() {
        return attachment;
    }

    public void setAttachment(Attachment attachment) {
        this.attachment = attachment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}