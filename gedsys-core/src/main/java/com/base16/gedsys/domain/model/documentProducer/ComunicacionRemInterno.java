/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "ComunicacionRemInterno", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "ComunicacionRemInterno.findAll", query = "SELECT c FROM ComunicacionRemInterno c")
    , @NamedQuery(name = "ComunicacionRemInterno.findById", query = "SELECT c FROM ComunicacionRemInterno c WHERE c.id = :id")})
public class ComunicacionRemInterno implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @JoinColumn(name = "comunicacion", referencedColumnName = "Id")
    @ManyToOne
    private Comunicacion comunicacion;
    @JoinColumn(name = "remitente", referencedColumnName = "Id")
    @ManyToOne
    private Usuario remitente;

    @Column(name = "docVisualizado")
    private Boolean docVisualizado;

    public ComunicacionRemInterno() {
    }

    public ComunicacionRemInterno(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Comunicacion getComunicacion() {
        return comunicacion;
    }

    public void setComunicacion(Comunicacion comunicacion) {
        this.comunicacion = comunicacion;
    }

    public Usuario getRemitente() {
        return remitente;
    }

    public void setRemitente(Usuario remitente) {
        this.remitente = remitente;
    }

    public Boolean getDocVisualizado() {
        return docVisualizado;
    }

    public void setDocVisualizado(Boolean docVisualizado) {
        this.docVisualizado = docVisualizado;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof ComunicacionRemInterno)) {
            return false;
        }
        ComunicacionRemInterno other = (ComunicacionRemInterno) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.documentProducer.ComunicacionRemInterno[ id=" + id + " ]";
    }

}
