/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import com.base16.gedsys.domain.model.documentProducer.Comunicacion;
import com.base16.gedsys.domain.model.documentProducer.Circular;
import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.documentProducer.Acta;
import com.base16.gedsys.domain.model.documentProducer.Certificado;
import com.base16.gedsys.domain.model.documentProducer.Constancia;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.documentProducer.Informe;
import com.base16.gedsys.domain.model.documentProducer.Resolucion;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaEspecifica;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "tipodocumental", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
        @NamedQuery(name = "TipoDocumental.findAll", query = "SELECT t FROM TipoDocumental t ORDER BY t.nombre ASC")
        , @NamedQuery(name = "TipoDocumental.findById", query = "SELECT t FROM TipoDocumental t WHERE t.id = :id")
        , @NamedQuery(name = "TipoDocumental.findByBorrado", query = "SELECT t FROM TipoDocumental t WHERE t.borrado = :borrado")
        , @NamedQuery(name = "TipoDocumental.findByDocumentoExterno", query = "SELECT t FROM TipoDocumental t WHERE t.documentoExterno = :documentoExterno")
        , @NamedQuery(name = "TipoDocumental.findByEstampadoCronologico", query = "SELECT t FROM TipoDocumental t WHERE t.estampadoCronologico = :estampadoCronologico")
        , @NamedQuery(name = "TipoDocumental.findByFechaCreacion", query = "SELECT t FROM TipoDocumental t WHERE t.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "TipoDocumental.findByFechaModificacion", query = "SELECT t FROM TipoDocumental t WHERE t.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "TipoDocumental.findByNombre", query = "SELECT t FROM TipoDocumental t WHERE t.nombre = :nombre")
        , @NamedQuery(name = "TipoDocumental.findByRequiereRespuesta", query = "SELECT t FROM TipoDocumental t WHERE t.requiereRespuesta = :requiereRespuesta")
        , @NamedQuery(name = "TipoDocumental.findByTiempoRespuesta", query = "SELECT t FROM TipoDocumental t WHERE t.tiempoRespuesta = :tiempoRespuesta")
        , @NamedQuery(name = "TipoDocumental.findByUnidadDocumental", query = "SELECT t FROM TipoDocumental t WHERE t.unidadDocumental = :unidadDocumental")
        , @NamedQuery(name = "TipoDocumental.findByTipoCalendario", query = "SELECT t FROM TipoDocumental t WHERE t.tipoCalendario = :tipoCalendario")
        , @NamedQuery(name = "TipoDocumental.findPQRSF", query = "SELECT t FROM TipoDocumental t WHERE t.esPQRSF = true and t.tipoIncidencia = :tipoIncidencia")
})
public class TipoDocumental implements Serializable {

    @OneToMany(mappedBy = "tipoDocumental")
    private List<Resolucion> resolucionList;

    @OneToMany(mappedBy = "tipoDocumental")
    private List<Acta> actaList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Circular> circularList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Comunicacion> comunicacionList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<SubSeriesTiposDocumentales> subSeriesTiposDocumentalesList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Constancia> constanciaList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Carta> cartaList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Informe> informeList;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Certificado> certificadoList;

    @Column(name = "ProducPorSoftware")
    private String producPorSoftware;

    @Column(name = "EntradaS")
    private Boolean entradaS;
    @Column(name = "AmarilloS")
    private Boolean amarilloS;
    @Column(name = "DiasAmarilloS")
    private Integer diasAmarilloS;
    @Column(name = "RojoS")
    private Boolean rojoS;
    @Column(name = "DiasRojoS")
    private Integer diasRojoS;
    @Column(name = "VencidoS")
    private Boolean vencidoS;
    @Column(name = "DiasVencidoS")
    private Integer diasVencidoS;
    @Column(name = "deleted")
    private Boolean deleted;

    @Column(name = "EntradaE")
    private Boolean entradaE;
    @Column(name = "DiasEntradaE")
    private Integer diasEntradaE;
    @Column(name = "AmarilloE")
    private Boolean amarilloE;
    @Column(name = "DiasAmarilloE")
    private Integer diasAmarilloE;
    @Column(name = "RojoE")
    private Boolean rojoE;
    @Column(name = "DiasRojoE")
    private Integer diasRojoE;
    @Column(name = "VencidoE")
    private Boolean vencidoE;
    @Column(name = "DiasVencidoE")
    private Integer diasVencidoE;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "DocumentoExterno")
    private Boolean documentoExterno;
    @Column(name = "EstampadoCronologico")
    private String estampadoCronologico;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "RequiereRespuesta")
    private Boolean requiereRespuesta;
    @Column(name = "TiempoRespuesta")
    private Integer tiempoRespuesta;
    @Column(name = "TipoCalendario")
    private String tipoCalendario;
    @Column(name = "UnidadDocumental")
    private Integer unidadDocumental;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @OneToMany(mappedBy = "tipoDocumental")
    private List<Documento> documentoList;
    @OneToMany(mappedBy = "tipoIncidencia")
    private List<IncidenciaGeneral> incidenciasAsociadas;
    @Column(name = "DiasRespuesta")
    private Integer diasRespuesta;
    @Column(name = "EsPQRSF")
    private Boolean esPQRSF;
    @Column(name = "tipoIncidencia")
    private String tipoIncidencia;

    @Column(name = "id_responsable")
    private Integer idResponsable;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_responsable", updatable = false, insertable = false)
    private Usuario responsable;

    public TipoDocumental() {
    }

    public TipoDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public Boolean getDocumentoExterno() {
        return documentoExterno;
    }

    public void setDocumentoExterno(Boolean documentoExterno) {
        this.documentoExterno = documentoExterno;
    }

    public String getEstampadoCronologico() {
        return estampadoCronologico;
    }

    public void setEstampadoCronologico(String estampadoCronologico) {
        this.estampadoCronologico = estampadoCronologico;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Boolean getRequiereRespuesta() {
        return requiereRespuesta;
    }

    public void setRequiereRespuesta(Boolean requiereRespuesta) {
        this.requiereRespuesta = requiereRespuesta;
    }

    public Integer getTiempoRespuesta() {
        return tiempoRespuesta;
    }

    public void setTiempoRespuesta(Integer tiempoRespuesta) {
        this.tiempoRespuesta = tiempoRespuesta;
    }

    public String getTipoCalendario() {
        return tipoCalendario;
    }

    public void setTipoCalendario(String tipoCalendario) {
        this.tipoCalendario = tipoCalendario;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof TipoDocumental)) {
            return false;
        }
        TipoDocumental other = (TipoDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return this.nombre;
    }

    public Integer getUnidadDocumental() {
        return unidadDocumental;
    }

    public void setUnidadDocumental(Integer unidadDocumental) {
        this.unidadDocumental = unidadDocumental;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @XmlTransient
    @JsonIgnore
    public List<Documento> getDocumentoList() {
        return documentoList;
    }

    public void setDocumentoList(List<Documento> documentoList) {
        this.documentoList = documentoList;
    }

    public Integer getDiasRespuesta() {
        return diasRespuesta;
    }

    public void setDiasRespuesta(Integer diasRespuesta) {
        this.diasRespuesta = diasRespuesta;
    }

    public Boolean getEsPQRSF() {
        return Optional.ofNullable(esPQRSF).orElse(false);
    }

    public void setEsPQRSF(Boolean esPQRSF) {
        this.esPQRSF = esPQRSF;
    }

    public Boolean getEntradaE() {
        return entradaE;
    }

    public void setEntradaE(Boolean entradaE) {
        this.entradaE = entradaE;
    }

    public Integer getDiasEntradaE() {
        return diasEntradaE;
    }

    public void setDiasEntradaE(Integer diasEntradaE) {
        this.diasEntradaE = diasEntradaE;
    }

    public Boolean getAmarilloE() {
        return amarilloE;
    }

    public void setAmarilloE(Boolean amarilloE) {
        this.amarilloE = amarilloE;
    }

    public Integer getDiasAmarilloE() {
        return diasAmarilloE;
    }

    public void setDiasAmarilloE(Integer diasAmarilloE) {
        this.diasAmarilloE = diasAmarilloE;
    }

    public Boolean getRojoE() {
        return rojoE;
    }

    public void setRojoE(Boolean rojoE) {
        this.rojoE = rojoE;
    }

    public Integer getDiasRojoE() {
        return diasRojoE;
    }

    public void setDiasRojoE(Integer diasRojoE) {
        this.diasRojoE = diasRojoE;
    }

    public Boolean getVencidoE() {
        return vencidoE;
    }

    public void setVencidoE(Boolean vencidoE) {
        this.vencidoE = vencidoE;
    }

    public Integer getDiasVencidoE() {
        return diasVencidoE;
    }

    public void setDiasVencidoE(Integer diasVencidoE) {
        this.diasVencidoE = diasVencidoE;
    }

    public Boolean getEntradaS() {
        return entradaS;
    }

    public void setEntradaS(Boolean entradaS) {
        this.entradaS = entradaS;
    }

    public Boolean getAmarilloS() {
        return amarilloS;
    }

    public void setAmarilloS(Boolean amarilloS) {
        this.amarilloS = amarilloS;
    }

    public Integer getDiasAmarilloS() {
        return diasAmarilloS;
    }

    public void setDiasAmarilloS(Integer diasAmarilloS) {
        this.diasAmarilloS = diasAmarilloS;
    }

    public Boolean getRojoS() {
        return rojoS;
    }

    public void setRojoS(Boolean rojoS) {
        this.rojoS = rojoS;
    }

    public Integer getDiasRojoS() {
        return diasRojoS;
    }

    public void setDiasRojoS(Integer diasRojoS) {
        this.diasRojoS = diasRojoS;
    }

    public Boolean getVencidoS() {
        return vencidoS;
    }

    public void setVencidoS(Boolean vencidoS) {
        this.vencidoS = vencidoS;
    }

    public Integer getDiasVencidoS() {
        return diasVencidoS;
    }

    public void setDiasVencidoS(Integer diasVencidoS) {
        this.diasVencidoS = diasVencidoS;
    }

    public String getProducPorSoftware() {
        return producPorSoftware;
    }

    public void setProducPorSoftware(String producPorSoftware) {
        this.producPorSoftware = producPorSoftware;
    }

    @XmlTransient
    @JsonIgnore
    public List<Acta> getActaList() {
        return actaList;
    }

    public void setActaList(List<Acta> actaList) {
        this.actaList = actaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Circular> getCircularList() {
        return circularList;
    }

    public void setCircularList(List<Circular> circularList) {
        this.circularList = circularList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Comunicacion> getComunicacionList() {
        return comunicacionList;
    }

    public void setComunicacionList(List<Comunicacion> comunicacionList) {
        this.comunicacionList = comunicacionList;
    }

    @XmlTransient
    @JsonIgnore
    public List<SubSeriesTiposDocumentales> getSubSeriesTiposDocumentalesList() {
        return subSeriesTiposDocumentalesList;
    }

    public void setSubSeriesTiposDocumentalesList(List<SubSeriesTiposDocumentales> subSeriesTiposDocumentalesList) {
        this.subSeriesTiposDocumentalesList = subSeriesTiposDocumentalesList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Constancia> getConstanciaList() {
        return constanciaList;
    }

    public void setConstanciaList(List<Constancia> constanciaList) {
        this.constanciaList = constanciaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Informe> getInformeList() {
        return informeList;
    }

    public void setInformeList(List<Informe> informeList) {
        this.informeList = informeList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Certificado> getCertificadoList() {
        return certificadoList;
    }

    public void setCertificadoList(List<Certificado> certificadoList) {
        this.certificadoList = certificadoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Resolucion> getResolucionList() {
        return resolucionList;
    }

    public void setResolucionList(List<Resolucion> resolucionList) {
        this.resolucionList = resolucionList;
    }

    public String getTipoIncidencia() {
        return tipoIncidencia;
    }

    public void setTipoIncidencia(String tipoIncidencia) {
        this.tipoIncidencia = tipoIncidencia;
    }

    public List<IncidenciaGeneral> getIncidenciasAsociadas() {
        return incidenciasAsociadas;
    }

    public void setIncidenciasAsociadas(List<IncidenciaGeneral> incidenciasAsociadas) {
        this.incidenciasAsociadas = incidenciasAsociadas;
    }

    public Integer getIdResponsable() {
        return idResponsable;
    }

    public void setIdResponsable(Integer idResponsable) {
        this.idResponsable = idResponsable;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
        this.idResponsable = responsable != null ?
                responsable.getId() :
                null;
    }
}
