/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "mediorecepcion", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
    @NamedQuery(name = "Mediorecepcion.findAll", query = "SELECT m FROM Mediorecepcion m")
    , @NamedQuery(name = "Mediorecepcion.findById", query = "SELECT m FROM Mediorecepcion m WHERE m.id = :id")
    , @NamedQuery(name = "Mediorecepcion.findByCodigo", query = "SELECT m FROM Mediorecepcion m WHERE m.codigo = :codigo")
    , @NamedQuery(name = "Mediorecepcion.findByNombre", query = "SELECT m FROM Mediorecepcion m WHERE m.nombre = :nombre")
    , @NamedQuery(name = "Mediorecepcion.findByFechaCreacion", query = "SELECT m FROM Mediorecepcion m WHERE m.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Mediorecepcion.findByFechaModificacion", query = "SELECT m FROM Mediorecepcion m WHERE m.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Mediorecepcion.findByEstado", query = "SELECT m FROM Mediorecepcion m WHERE m.estado = :estado")})
public class Mediorecepcion implements Serializable {
    @OneToMany(mappedBy = "mediorecepcion")
    private List<Carta> cartaList;

    @Column(name = "Estado")
    private Boolean estado;

    @Column(name = "correo")
    private Boolean correo;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Codigo")
    private String codigo;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @OneToMany(mappedBy = "medioEnvio")
    private List<Documento> documentoList;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @Column(name = "deleted")
    private Boolean deleted;

    public Mediorecepcion() {
    }

    public Mediorecepcion(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }


    @XmlTransient
    @JsonIgnore
    public List<Documento> getDocumentoList() {
        return documentoList;
    }

    public void setDocumentoList(List<Documento> documentoList) {
        this.documentoList = documentoList;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Mediorecepcion)) {
            return false;
        }
        Mediorecepcion other = (Mediorecepcion) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Mediorecepcion[ id=" + id + " ]";
    }

    public Boolean getEstado() {
        return estado;
    }

    public void setEstado(Boolean estado) {
        this.estado = estado;
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    public Boolean getCorreo() {
        return correo;
    }

    public void setCorreo(Boolean correo) {
        this.correo = correo;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
