package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.Enumeration.EstadoIncidenciaEspecificaEnum;
import com.base16.gedsys.Enumeration.EstadosIncidenciaGeneralEnum;
import com.base16.gedsys.domain.model.pqrsd.TrazabilidadPQRSF;

import javax.persistence.PrePersist;
import java.util.Date;
import java.util.Optional;

public class TrazabilidadPQRSFListener {
    @PrePersist
    public void onCreate(TrazabilidadPQRSF t) {
        t.setFechaRegistro(new Date());
        /**
         * Registro automático de trazabilidad para las incidencias generales
         */
        if (Optional.ofNullable(t.getPqrsfGeneral()).isPresent()
                && (!Optional.ofNullable(t.getAccion()).isPresent() || t.getAccion().isEmpty())) {
            EstadosIncidenciaGeneralEnum estado =
                    EstadosIncidenciaGeneralEnum.traducirEstado(t.getPqrsfGeneral().getEstado());
            String tipoIncidencia = t.getPqrsfGeneral().getTipoIncidencia().getTipoIncidencia();
            switch (tipoIncidencia) {
                case "p":
                    tipoIncidencia = "Petición";
                    break;
                case "q":
                    tipoIncidencia = "Queja";
                    break;
                case "r":
                    tipoIncidencia = "Reclamo";
                    break;
                case "s":
                    tipoIncidencia = "Solicitud";
                    break;
                case "f":
                    tipoIncidencia = "Felicitación";
                    break;
            }
            switch (estado) {
                case RECIBIDA:
                    t.setAccion(String.format("%s registrada vía Web", tipoIncidencia));
                    break;
                case DESCARGOS:
                    t.setAccion(String.format("%s comunicada a los implicados", tipoIncidencia));
                    break;
                case PENDIENTE_RESPUESTA:
                    t.setAccion(String.format("%s respondida por los implicados y pendiente de respuesta", tipoIncidencia));
                    break;
                case RESPONDIDA:
                    t.setAccion(String.format("%s respondida Radicado N° %s", tipoIncidencia,
                            t.getPqrsfGeneral().getRespuesta().getConsecutivo()));
                    break;
                case FINALIZADA:
                    t.setAccion(String.format("%s finalizada", tipoIncidencia));
                    break;
                case DESCARTADA:
                    break;
            }

        }

        /**
         * Incidencias específicas
         */
        if (Optional.ofNullable(t.getPqrsfEspecifica()).isPresent()
                && (!Optional.ofNullable(t.getAccion()).isPresent() || t.getAccion().isEmpty())) {
            EstadoIncidenciaEspecificaEnum estado =
                    EstadoIncidenciaEspecificaEnum.traducirEstado(t.getPqrsfEspecifica().getEstado());

            switch (estado){
                case PENDIENTE:
                    t.setAccion("Incidencia comunicada al implicado");
                    break;
                case RESPONDIDA:
                    t.setAccion("Incidencia respondida por el implicado");
                    break;
                case FELICITACION:
                    t.setAccion("Felicitación recibida");
                    break;
            }

        }
    }
}
