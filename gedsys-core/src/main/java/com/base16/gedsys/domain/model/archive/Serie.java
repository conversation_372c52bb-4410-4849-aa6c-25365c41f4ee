/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import com.base16.gedsys.domain.model.document.Consecutivo;
import com.base16.gedsys.domain.model.documentProducer.Comunicacion;
import com.base16.gedsys.domain.model.documentProducer.Circular;
import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.documentProducer.Acta;
import com.base16.gedsys.domain.model.documentProducer.Certificado;
import com.base16.gedsys.domain.model.documentProducer.Constancia;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.documentProducer.Informe;
import com.base16.gedsys.domain.model.documentProducer.Resolucion;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "serie", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
    @NamedQuery(name = "Serie.findAll", query = "SELECT s FROM Serie s"),
    @NamedQuery(name = "Serie.findById", query = "SELECT s FROM Serie s WHERE s.id = :id"),
    @NamedQuery(name = "Serie.findByBorrado", query = "SELECT s FROM Serie s WHERE s.borrado = :borrado"),
    @NamedQuery(name = "Serie.findByFechaCreacion", query = "SELECT s FROM Serie s WHERE s.fechaCreacion = :fechaCreacion"),
    @NamedQuery(name = "Serie.findByFechaModificacion", query = "SELECT s FROM Serie s WHERE s.fechaModificacion = :fechaModificacion"),
    @NamedQuery(name = "Serie.findBySeccionSubSeccion", query = "SELECT DISTINCT s FROM Subseccionsubserie sbsb "
            + "inner join sbsb.subserie ss "
            + "inner join ss.serie s "
            + "inner join sbsb.subseccion sb "
            + "WHERE sb.id = :seccion"),
    @NamedQuery(name = "Serie.findByNombre", query = "SELECT s FROM Serie s WHERE s.nombre = :nombre")})

public class Serie implements Serializable {

    @OneToMany(mappedBy = "serie")
    private List<Resolucion> resolucionList;

    @OneToMany(mappedBy = "serie")
    private List<Consecutivo> consecutivoList;

    @OneToMany(mappedBy = "serie")
    private List<SubSeriesTiposDocumentales> subSeriesTiposDocumentalesList;

    @OneToMany(mappedBy = "serie")
    private List<Documento> documentoList;

    @OneToMany(mappedBy = "serie")
    private List<Constancia> constanciaList;

    @OneToMany(mappedBy = "serie")
    private List<Carta> cartaList;
    @OneToMany(mappedBy = "serie")
    private List<Acta> actaList;
    @OneToMany(mappedBy = "serie")
    private List<Informe> informeList;
    @OneToMany(mappedBy = "serie")
    private List<Circular> circularList;
    @OneToMany(mappedBy = "serie")
    private List<Certificado> certificadoList;
    @OneToMany(mappedBy = "serie")
    private List<Comunicacion> comunicacionList;


    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "deleted")
    private Boolean deleted;
    @OneToMany(mappedBy = "serie",
            cascade = CascadeType.ALL)
    private Collection<SubSerie> subSerieCollection;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @Column(name = "Codigo")
    private String codigo;

    public Serie() {
    }

    public Serie(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Boolean getDeleted() { return deleted; }

    public void setDeleted(Boolean deleted) {this.deleted = deleted;}

    @XmlTransient
    @JsonIgnore
    public Collection<SubSerie> getSubSerieCollection() {
        return subSerieCollection;
    }

    public void setSubSerieCollection(Collection<SubSerie> subSerieCollection) {
        this.subSerieCollection = subSerieCollection;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Serie)) {
            return false;
        }
        Serie other = (Serie) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return this.nombre;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Acta> getActaList() {
        return actaList;
    }

    public void setActaList(List<Acta> actaList) {
        this.actaList = actaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Informe> getInformeList() {
        return informeList;
    }

    public void setInformeList(List<Informe> informeList) {
        this.informeList = informeList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Circular> getCircularList() {
        return circularList;
    }

    public void setCircularList(List<Circular> circularList) {
        this.circularList = circularList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Certificado> getCertificadoList() {
        return certificadoList;
    }

    public void setCertificadoList(List<Certificado> certificadoList) {
        this.certificadoList = certificadoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Comunicacion> getComunicacionList() {
        return comunicacionList;
    }

    public void setComunicacionList(List<Comunicacion> comunicacionList) {
        this.comunicacionList = comunicacionList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Constancia> getConstanciaList() {
        return constanciaList;
    }

    public void setConstanciaList(List<Constancia> constanciaList) {
        this.constanciaList = constanciaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Documento> getDocumentoList() {
        return documentoList;
    }

    public void setDocumentoList(List<Documento> documentoList) {
        this.documentoList = documentoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<SubSeriesTiposDocumentales> getSubSeriesTiposDocumentalesList() {
        return subSeriesTiposDocumentalesList;
    }

    public void setSubSeriesTiposDocumentalesList(List<SubSeriesTiposDocumentales> subSeriesTiposDocumentalesList) {
        this.subSeriesTiposDocumentalesList = subSeriesTiposDocumentalesList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Consecutivo> getConsecutivoList() {
        return consecutivoList;
    }

    public void setConsecutivoList(List<Consecutivo> consecutivoList) {
        this.consecutivoList = consecutivoList;
    }



    @XmlTransient
    @JsonIgnore
    public List<Resolucion> getResolucionList() {
        return resolucionList;
    }

    public void setResolucionList(List<Resolucion> resolucionList) {
        this.resolucionList = resolucionList;
    }

}
