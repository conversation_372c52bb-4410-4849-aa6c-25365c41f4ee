package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.document.Entidad;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;
import java.util.Optional;

public class EntidadListener {
    @PrePersist
    public void onCreate(Entidad e){
        e.setFechaCreacion(new Date());
        e.setTemporal(Optional.ofNullable(e.getTemporal()).isPresent()?e.getTemporal():false);
        e.setDeleted(false);
    }

    @PreUpdate
    public void onUpdate(Entidad e){
        e.setFechaModificacion(new Date());
    }
}
