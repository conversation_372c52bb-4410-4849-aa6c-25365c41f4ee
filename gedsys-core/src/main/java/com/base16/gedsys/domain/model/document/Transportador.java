/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.sendTemplate.PlanillaEnvio;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "transportador", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Transportador.findAll", query = "SELECT t FROM Transportador t")
    , @NamedQuery(name = "Transportador.findById", query = "SELECT t FROM Transportador t WHERE t.id = :id")
    , @NamedQuery(name = "Transportador.findByBorrado", query = "SELECT t FROM Transportador t WHERE t.borrado = :borrado")
    , @NamedQuery(name = "Transportador.findByCelular", query = "SELECT t FROM Transportador t WHERE t.celular = :celular")
    , @NamedQuery(name = "Transportador.findByEmail", query = "SELECT t FROM Transportador t WHERE t.email = :email")
    , @NamedQuery(name = "Transportador.findByFax", query = "SELECT t FROM Transportador t WHERE t.fax = :fax")
    , @NamedQuery(name = "Transportador.findByFechaCreacion", query = "SELECT t FROM Transportador t WHERE t.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Transportador.findByFechaModificacion", query = "SELECT t FROM Transportador t WHERE t.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Transportador.findByNombre", query = "SELECT t FROM Transportador t WHERE t.nombre = :nombre")
    , @NamedQuery(name = "Transportador.findByNumeroDocumento", query = "SELECT t FROM Transportador t WHERE t.numeroDocumento = :numeroDocumento")
    , @NamedQuery(name = "Transportador.findByTelefono", query = "SELECT t FROM Transportador t WHERE t.telefono = :telefono")
    , @NamedQuery(name = "Transportador.findByTipoDocumento", query = "SELECT t FROM Transportador t WHERE t.tipoDocumento = :tipoDocumento")})
public class Transportador implements Serializable {

    @OneToMany(mappedBy = "transportador")
    private List<PlanillaEnvio> planillaEnvioList;

    @Column(name = "TipoTransportador")
    private String tipoTransportador;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "Celular")
    private String celular;
    @Column(name = "Email")
    private String email;
    @Column(name = "Fax")
    private String fax;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "NumeroDocumento")
    private String numeroDocumento;
    @Column(name = "Telefono")
    private String telefono;
    @Column(name = "TipoDocumento")
    private String tipoDocumento;
    @OneToMany(mappedBy = "transportador")
    private Collection<Documento> documentoCollection;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    public Transportador() {
    }

    public Transportador(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getTelefono() {
        return telefono;
    }

    public void setTelefono(String telefono) {
        this.telefono = telefono;
    }

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<Documento> getDocumentoCollection() {
        return documentoCollection;
    }

    public void setDocumentoCollection(Collection<Documento> documentoCollection) {
        this.documentoCollection = documentoCollection;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Transportador)) {
            return false;
        }
        Transportador other = (Transportador) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.Transportador[ id=" + id + " ]";
    }

    public String getTipoTransportador() {
        return tipoTransportador;
    }

    public void setTipoTransportador(String tipoTransportador) {
        this.tipoTransportador = tipoTransportador;
    }

    @XmlTransient
    @JsonIgnore
    public List<PlanillaEnvio> getPlanillaEnvioList() {
        return planillaEnvioList;
    }

    public void setPlanillaEnvioList(List<PlanillaEnvio> planillaEnvioList) {
        this.planillaEnvioList = planillaEnvioList;
    }
    
}
