/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.entityListeners.TrazabilidadListener;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@EntityListeners(TrazabilidadListener.class)
@Table(name = "trazabilidad", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Trazabilidad.findAll", query = "SELECT t FROM Trazabilidad t")
    , @NamedQuery(name = "Trazabilidad.findById", query = "SELECT t FROM Trazabilidad t WHERE t.id = :id")
    , @NamedQuery(name = "Trazabilidad.findByAccion", query = "SELECT t FROM Trazabilidad t WHERE t.accion = :accion")
    , @NamedQuery(name = "Trazabilidad.findByFechaRegistro", query = "SELECT t FROM Trazabilidad t WHERE t.fechaRegistro = :fechaRegistro")
    , @NamedQuery(name = "Trazabilidad.findByUsuario", query = "SELECT t FROM Trazabilidad t WHERE t.usuario = :usuario")
    , @NamedQuery(name = "Trazabilidad.findByCodigoDocumento", query = "SELECT t FROM Trazabilidad t WHERE t.documento = :documento")})
public class Trazabilidad implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "Id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "Accion")
    private String accion;
    @Column(name = "FechaRegistro")
    private Date fechaRegistro;
    @JoinColumn(name = "Documento", referencedColumnName = "Id")
    @ManyToOne
    private Documento documento;
    @JoinColumn(name = "Usuario", referencedColumnName = "Id")
    @ManyToOne
    private Usuario usuario;

    public Trazabilidad() {
    }

    public Trazabilidad(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccion() {
        return accion;
    }

    public void setAccion(String accion) {
        this.accion = accion;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public Documento getDocumento() {
        return documento;
    }

    public void setDocumento(Documento documento) {
        this.documento = documento;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Trazabilidad)) {
            return false;
        }
        Trazabilidad other = (Trazabilidad) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Trazabilidad[ id=" + id + " ]";
    }

}
