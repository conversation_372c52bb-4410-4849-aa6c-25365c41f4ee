/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.archive.Serie;
import com.base16.gedsys.domain.model.archive.SubSerie;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;

import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "acta", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "Acta.findAll", query = "SELECT a FROM Acta a")
        , @NamedQuery(name = "Acta.findById", query = "SELECT a FROM Acta a WHERE a.id = :id")
        , @NamedQuery(name = "Acta.findByLugar", query = "SELECT a FROM Acta a WHERE a.lugar = :lugar")
        , @NamedQuery(name = "Acta.findByFechaCreacion", query = "SELECT a FROM Acta a WHERE a.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "Acta.findByFechaModificacion", query = "SELECT a FROM Acta a WHERE a.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "Acta.findByFechaFirma", query = "SELECT a FROM Acta a WHERE a.fechaFirma = :fechaFirma")
        , @NamedQuery(name = "Acta.findByEstadoYUsuario", query = "SELECT a FROM Acta a WHERE a.estado = :estado AND (a.presidente = :usuario or a.creadoPor = :creadoPor or a.secretaria = :usuario)")
        , @NamedQuery(name = "Acta.findByEstado", query = "SELECT a FROM Acta a WHERE a.estado = :estado")})
public class Acta implements Serializable {

    @Column(name = "active")
    private Boolean active;

    @Size(max = 50)
    @Column(name = "numero")
    private String numero;

    @Column(name = "isExtern")
    private Boolean isExtern;

    @Column(name = "fechaDocumento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaDocumento;

    @Size(max = 255)
    @Column(name = "Anexos")
    private String anexos;

    @JoinColumn(name = "municipio", referencedColumnName = "Id")
    @ManyToOne
    private Municipio municipio;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "ConCopiaExterna")
    private String conCopiaExterna;

    @Size(max = 50)
    @Column(name = "Estado")
    private String estado;

    @JoinColumn(name = "quienProyecto", referencedColumnName = "Id")
    @ManyToOne
    private Usuario quienProyecto;

    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;

    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;

    @JoinColumn(name = "SeccionSubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccionSubSeccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Column(name = "Titulo")
    private String titulo;
    @Column(name = "Lugar")
    private String lugar;
    @Lob
    @Column(name = "Orden")
    private String orden;
    @Lob
    @Column(name = "Desarrollo")
    private String desarrollo;
    @Lob
    @Column(name = "Convocatoria")
    private String convocatoria;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "FechaFirma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "acta")
    private List<ActaAusente> actaausenteList;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario modificadoPor;
    @JoinColumn(name = "Presidente", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario presidente;
    @JoinColumn(name = "Secretaria", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario secretaria;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "acta")
    private List<ActaAsistente> actaasistenteList;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "acta")
    private List<ActaInvitado> actainvitadoList;
    @Column(name = "HoraInicio")
    @Temporal(TemporalType.TIMESTAMP)
    private Date horaInicio;
    @Column(name = "HoraFinalizacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date horaFinalizacion;
    @Column(name = "TipoComunicacion")
    private String tipoComunicacion;
    @Column(name = "FechaFirmaSecretaria")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaSecretaria;
    @Column(name = "FechaFirmaPresidente")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaPresidente;

    @Column(name = "InvitadosExternos")
    private String invitadosExternos;

    @Lob
    @Column(name = "comentario")
    private String comentario;

    @JoinColumn(name = "DocumentoFirmado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoFirmado;

    public Acta() {
    }

    public Acta(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getLugar() {
        return lugar;
    }

    public void setLugar(String lugar) {
        this.lugar = lugar;
    }

    public String getOrden() {
        return orden;
    }

    public void setOrden(String orden) {
        this.orden = orden;
    }

    public String getDesarrollo() {
        return desarrollo;
    }

    public void setDesarrollo(String desarrollo) {
        this.desarrollo = desarrollo;
    }

    public String getConvocatoria() {
        return convocatoria;
    }

    public void setConvocatoria(String convocatoria) {
        this.convocatoria = convocatoria;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    @XmlTransient
    @JsonIgnore
    public List<ActaAusente> getActaausenteList() {
        return actaausenteList;
    }

    public void setActaausenteList(List<ActaAusente> actaausenteList) {
        this.actaausenteList = actaausenteList;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Usuario getPresidente() {
        return presidente;
    }

    public void setPresidente(Usuario presidente) {
        this.presidente = presidente;
    }

    public Usuario getSecretaria() {
        return secretaria;
    }

    public void setSecretaria(Usuario secretaria) {
        this.secretaria = secretaria;
    }

    @XmlTransient
    @JsonIgnore
    public List<ActaAsistente> getActaasistenteList() {
        return actaasistenteList;
    }

    public void setActaasistenteList(List<ActaAsistente> actaasistenteList) {
        this.actaasistenteList = actaasistenteList;
    }

    @XmlTransient
    @JsonIgnore
    public List<ActaInvitado> getActainvitadoList() {
        return actainvitadoList;
    }

    public void setActainvitadoList(List<ActaInvitado> actainvitadoList) {
        this.actainvitadoList = actainvitadoList;
    }

    public String getTipoComunicacion() {
        return tipoComunicacion;
    }

    public void setTipoComunicacion(String tipoComunicacion) {
        this.tipoComunicacion = tipoComunicacion;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Acta)) {
            return false;
        }
        Acta other = (Acta) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Acta[ id=" + id + " ]";
    }

    public Date getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(Date horaInicio) {
        this.horaInicio = horaInicio;
    }

    public Date getHoraFinalizacion() {
        return horaFinalizacion;
    }

    public void setHoraFinalizacion(Date horaFinalizacion) {
        this.horaFinalizacion = horaFinalizacion;
    }

    public void limpiar() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    public Date getFechaFirmaSecretaria() {
        return fechaFirmaSecretaria;
    }

    public void setFechaFirmaSecretaria(Date fechaFirmaSecretaria) {
        this.fechaFirmaSecretaria = fechaFirmaSecretaria;
    }

    public Date getFechaFirmaPresidente() {
        return fechaFirmaPresidente;
    }

    public void setFechaFirmaPresidente(Date fechaFirmaPresidente) {
        this.fechaFirmaPresidente = fechaFirmaPresidente;
    }

    public SeccionSubSeccion getSeccionSubSeccion() {
        return seccionSubSeccion;
    }

    public void setSeccionSubSeccion(SeccionSubSeccion seccionSubSeccion) {
        this.seccionSubSeccion = seccionSubSeccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    public Usuario getQuienProyecto() {
        return quienProyecto;
    }

    public void setQuienProyecto(Usuario quienProyecto) {
        this.quienProyecto = quienProyecto;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getConCopiaExterna() {
        return conCopiaExterna;
    }

    public void setConCopiaExterna(String conCopiaExterna) {
        this.conCopiaExterna = conCopiaExterna;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Municipio getMunicipio() {
        return municipio;
    }

    public void setMunicipio(Municipio municipio) {
        this.municipio = municipio;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    public Date getFechaDocumento() {
        return fechaDocumento;
    }

    public void setFechaDocumento(Date fechaDocumento) {
        this.fechaDocumento = fechaDocumento;
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }


    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getInvitadosExternos() {
        return invitadosExternos;
    }

    public void setInvitadosExternos(String invitadosExternos) {
        this.invitadosExternos = invitadosExternos;
    }

    public Documento getDocumentoFirmado() {
        return documentoFirmado;
    }

    public void setDocumentoFirmado(Documento documentoFirmado) {
        this.documentoFirmado = documentoFirmado;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
