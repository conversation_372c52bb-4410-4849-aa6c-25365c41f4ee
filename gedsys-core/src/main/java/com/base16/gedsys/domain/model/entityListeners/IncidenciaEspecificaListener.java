package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.pqrsd.IncidenciaEspecifica;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;
import java.util.Optional;

public class IncidenciaEspecificaListener {
    @PrePersist
    public void onCreate(IncidenciaEspecifica e) {
        e.setFechaCreacion(new Date());
    }

    @PreUpdate
    public void onUpdate(IncidenciaEspecifica e){
        e.setFechaModificacion(new Date());
    }
}
