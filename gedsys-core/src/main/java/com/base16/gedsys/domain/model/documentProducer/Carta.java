/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.archive.Serie;
import com.base16.gedsys.domain.model.archive.SubSerie;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.document.Anexos;
import com.base16.gedsys.domain.model.document.Entidad;
import com.base16.gedsys.domain.model.document.Mediorecepcion;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "carta", schema = "dbo")
@XmlRootElement
@AdditionalCriteria("this.active = true")
@NamedQueries({
        @NamedQuery(name = "Carta.findAll", query = "SELECT c FROM Carta c ")
        ,
        @NamedQuery(name = "Carta.findById", query = "SELECT c FROM Carta c WHERE c.id = :id")
        ,
        @NamedQuery(name = "Carta.findByDocumentoFirmado", query = "SELECT c FROM Carta c WHERE c.documentoFirmado = :documentoFirmado")
        ,
        @NamedQuery(name = "Carta.findByTratamiento", query = "SELECT c FROM Carta c WHERE c.tratamiento = :tratamiento")
        ,
        @NamedQuery(name = "Carta.findByDestinatario", query = "SELECT c FROM Carta c WHERE c.destinatario = :destinatario")
        ,
        @NamedQuery(name = "Carta.findByCargo", query = "SELECT c FROM Carta c WHERE c.cargo = :cargo")
        ,
        @NamedQuery(name = "Carta.findByAsunto", query = "SELECT c FROM Carta c WHERE c.asunto = :asunto")
        ,
        @NamedQuery(name = "Carta.findByFechaCreacion", query = "SELECT c FROM Carta c WHERE c.fechaCreacion = :fechaCreacion")
        ,
        @NamedQuery(name = "Carta.findByFechaModificacion", query = "SELECT c FROM Carta c WHERE c.fechaModificacion = :fechaModificacion")
        ,
        @NamedQuery(name = "Carta.findByFechaFirma", query = "SELECT c FROM Carta c WHERE c.fechaFirma = :fechaFirma")
        ,
        @NamedQuery(name = "Carta.findByEstado", query = "SELECT c FROM Carta c WHERE c.estado = :estado")
        ,
        @NamedQuery(name = "Carta.findByEstadoYUsuario", query = "SELECT c FROM Carta c WHERE c.estado = :estado AND (c.remitente = :usuario OR c.creadoPor = :creadoPor OR c.responsable = :usuario ) ORDER BY c.fechaCreacion ASC")
        ,
        @NamedQuery(name = "Carta.findByDate", query = "SELECT c FROM Carta c WHERE c.fechaCreacion BETWEEN :fechaInicial AND :fechaFinal" )
        ,
        @NamedQuery(name = "Carta.findByDocumentoPadre", query = "SELECT c FROM Carta c WHERE c.documentoPadre = :documentoPadre")
        ,
        @NamedQuery(name = "Carta.findByIncidenciaGeneral", query = "SELECT c FROM Carta c WHERE c.incidenciaGeneral = :idIncidenciaGeneral")})
public class Carta implements Serializable {

    @Column(name = "active")
    private Boolean active;

    @OneToMany(mappedBy = "carta")
    private List<Anexos> anexosList;

    @Column(name = "Dias")
    private Integer dias;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "email")
    private String email;

    @Column(name = "isExtern")
    private Boolean isExtern;

    @Size(max = 255)
    @Column(name = "cargoFirmaUno")
    private String cargoFirmaUno;
    @Lob
    @Column(name = "comentario")
    private String comentario;
    @Size(max = 255)
    @Column(name = "cargoFirmaDos")
    private String cargoFirmaDos;

    @JoinColumn(name = "incidenciaGeneral", referencedColumnName = "Id")
    @OneToOne(cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    private IncidenciaGeneral incidenciaGeneral;

    @JoinColumn(name = "quienProyecto", referencedColumnName = "Id")
    @ManyToOne
    private Usuario quienProyecto;

    @JoinColumn(name = "entidad", referencedColumnName = "id")
    @ManyToOne
    private Entidad entidad;

    @JoinColumn(name = "Remitente2", referencedColumnName = "Id")
    @ManyToOne
    private Usuario remitente2;

    @JoinColumn(name = "mediorecepcion", referencedColumnName = "Id")
    @ManyToOne
    private Mediorecepcion mediorecepcion;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Column(name = "Tratamiento")
    private String tratamiento;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Destinatario")
    private String destinatario;
    @Column(name = "Cargo")
    private String cargo;
    @Column(name = "Empresa")
    private String empresa;
    @Column(name = "Direccion")
    private String direccion;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Asunto")
    private String asunto;
    @Lob
    @Column(name = "Contenido")
    private String contenido;
    @Lob
    @Column(name = "Despedida")
    private String despedida;
    @Lob
    @Column(name = "concopiaExterna")
    private String concopiaExterna;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "FechaFirma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;
    @Column(name = "fechaFirmaRem1")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaRem1;
    @Column(name = "fechaFirmaRem2")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirmaRem2;
    @Column(name = "Estado")
    private String estado;

    @Column(name = "TipoComunicacion")
    private String tipoComunicacion;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "carta")
    private List<CartaConCopia> cartaccList;
    @JoinColumn(name = "Remitente", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario remitente;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario modificadoPor;
    @JoinColumn(name = "Ciudad", referencedColumnName = "Id")
    @ManyToOne
    private Municipio ciudad;
    @JoinColumn(name = "DocumentoPadre", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoPadre;

    @JoinColumn(name = "DocumentoFirmado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoFirmado;

    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;

    @Column(name = "Copia")
    private Boolean copia;
    @JoinColumn(name = "SeccionSubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccionSubSeccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;

    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "carta")
    private Collection<CartaConCopia> cartaccCollection;

    @Column(name = "Anexos")
    private String anexos;

    @Column(name = "nombreArchivoCSV")
    private String nombreArchivoCSV;

    @Column(name = "masiva")
    private Boolean masiva;

    @Column(name = "producidaMasivamente")
    private Boolean producidaMasivamente;

    public Carta() {

    }

    public Carta(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    public String getTratamiento() {
        return tratamiento;
    }

    public void setTratamiento(String tratamiento) {
        this.tratamiento = tratamiento;
    }

    public String getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(String destinatario) {
        this.destinatario = destinatario;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getContenido() {
        return contenido;
    }

    public void setContenido(String contenido) {
        this.contenido = contenido;
    }

    public String getDespedida() {
        return despedida;
    }

    public void setDespedida(String despedida) {
        this.despedida = despedida;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public Usuario getRemitente() {
        return remitente;
    }

    public void setRemitente(Usuario remitente) {
        this.remitente = remitente;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @XmlTransient
    @JsonIgnore
    public List<CartaConCopia> getCartaccList() {
        return cartaccList;
    }

    public void setCartaccList(List<CartaConCopia> cartaccList) {
        this.cartaccList = cartaccList;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Carta)) {
            return false;
        }
        Carta other = (Carta) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Carta[ id=" + id + " ]";
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getDireccion() {
        return direccion;
    }

    public void setDireccion(String direccion) {
        this.direccion = direccion;
    }

    public Municipio getCiudad() {
        return ciudad;
    }

    public void setCiudad(Municipio ciudad) {
        this.ciudad = ciudad;
    }

    public Documento getDocumentoPadre() {
        return documentoPadre;
    }

    public void setDocumentoPadre(Documento documentoPadre) {
        this.documentoPadre = documentoPadre;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<CartaConCopia> getCartaccCollection() {
        return cartaccCollection;
    }

    public void setCartaccCollection(Collection<CartaConCopia> cartaccCollection) {
        this.cartaccCollection = cartaccCollection;
    }

    public String getTipoComunicacion() {
        return tipoComunicacion;
    }

    public void setTipoComunicacion(String tipoComunicacion) {
        this.tipoComunicacion = tipoComunicacion;
    }

    public Boolean getCopia() {
        return copia;
    }

    public void setCopia(Boolean copia) {
        this.copia = copia;
    }

    public SeccionSubSeccion getSeccionSubSeccion() {
        return seccionSubSeccion;
    }

    public void setSeccionSubSeccion(SeccionSubSeccion seccionSubSeccion) {
        this.seccionSubSeccion = seccionSubSeccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    public String getConcopiaExterna() {
        return concopiaExterna;
    }

    public void setConcopiaExterna(String concopiaExterna) {
        this.concopiaExterna = concopiaExterna;
    }

    public Mediorecepcion getMediorecepcion() {
        return mediorecepcion;
    }

    public void setMediorecepcion(Mediorecepcion mediorecepcion) {
        this.mediorecepcion = mediorecepcion;
    }

    public Usuario getRemitente2() {
        return remitente2;
    }

    public void setRemitente2(Usuario remitente2) {
        this.remitente2 = remitente2;
    }

    public Entidad getEntidad() {
        return entidad;
    }

    public void setEntidad(Entidad entidad) {
        this.entidad = entidad;
    }

    public Usuario getQuienProyecto() {
        return quienProyecto;
    }

    public void setQuienProyecto(Usuario quienProyecto) {
        this.quienProyecto = quienProyecto;
    }

    public IncidenciaGeneral getIncidenciaGeneral() {
        return incidenciaGeneral;
    }

    public void setIncidenciaGeneral(IncidenciaGeneral idIncidenciaGeneral) {
        this.incidenciaGeneral = idIncidenciaGeneral;
    }

    public String getCargoFirmaUno() {
        return cargoFirmaUno;
    }

    public void setCargoFirmaUno(String cargoFirmaUno) {
        this.cargoFirmaUno = cargoFirmaUno;
    }

    public String getCargoFirmaDos() {
        return cargoFirmaDos;
    }

    public void setCargoFirmaDos(String cargoFirmaDos) {
        this.cargoFirmaDos = cargoFirmaDos;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public Date getFechaFirmaRem1() {
        return fechaFirmaRem1;
    }

    public void setFechaFirmaRem1(Date fechaFirmaRem1) {
        this.fechaFirmaRem1 = fechaFirmaRem1;
    }

    public Date getFechaFirmaRem2() {
        return fechaFirmaRem2;
    }

    public void setFechaFirmaRem2(Date fechaFirmaRem2) {
        this.fechaFirmaRem2 = fechaFirmaRem2;
    }

    public Documento getDocumentoFirmado() {
        return documentoFirmado;
    }

    public void setDocumentoFirmado(Documento documentoFirmado) {
        this.documentoFirmado = documentoFirmado;
    }

    public String getNombreArchivoCSV() {
        return nombreArchivoCSV;
    }

    public void setNombreArchivoCSV(String rutaArchivoCSV) {
        this.nombreArchivoCSV = rutaArchivoCSV;
    }

    public Boolean getMasiva() {
        return masiva;
    }

    public void setMasiva(Boolean masiva) {
        this.masiva = masiva;
    }

    public Boolean getProducidaMasivamente() {
        return producidaMasivamente;
    }

    public void setProducidaMasivamente(Boolean producidaMasivamente) {
        this.producidaMasivamente = producidaMasivamente;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }


    @XmlTransient
    @JsonIgnore
    public List<Anexos> getAnexosList() {
        return anexosList;
    }

    public void setAnexosList(List<Anexos> anexosList) {
        this.anexosList = anexosList;
    }
}
