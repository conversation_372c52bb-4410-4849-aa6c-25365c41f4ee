/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "TrazabilidadProdDoc", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "TrazabilidadProdDoc.findAll", query = "SELECT t FROM TrazabilidadProdDoc t")
    , @NamedQuery(name = "TrazabilidadProdDoc.findById", query = "SELECT t FROM TrazabilidadProdDoc t WHERE t.id = :id")
    , @NamedQuery(name = "TrazabilidadProdDoc.findByFechaRegistro", query = "SELECT t FROM TrazabilidadProdDoc t WHERE t.fechaRegistro = :fechaRegistro")
    , @NamedQuery(name = "TrazabilidadProdDoc.findByTipoDocumento", query = "SELECT t FROM TrazabilidadProdDoc t WHERE t.tipoDocumento = :tipoDocumento")})
public class TrazabilidadProdDoc implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @Lob
    @Size(max = 255)
    @Column(name = "accion")
    private String accion;
    @Column(name = "fechaRegistro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;
    @Size(max = 50)
    @Column(name = "tipoDocumento")
    private String tipoDocumento;
    @JoinColumn(name = "usuario", referencedColumnName = "Id")
    @ManyToOne
    private Usuario usuario;
    @Column(name = "idTipoDocumento")
    private Integer idTipoDocumento;

    public TrazabilidadProdDoc() {
    }

    public TrazabilidadProdDoc(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAccion() {
        return accion;
    }

    public void setAccion(String accion) {
        this.accion = accion;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof TrazabilidadProdDoc)) {
            return false;
        }
        TrazabilidadProdDoc other = (TrazabilidadProdDoc) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.documentProducer.TrazabilidadProdDoc[ id=" + id + " ]";
    }

    public Integer getIdTipoDocumento() {
        return idTipoDocumento;
    }

    public void setIdTipoDocumento(Integer idTipoDocumento) {
        this.idTipoDocumento = idTipoDocumento;
    }
}
