package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.user.Usuario;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;

public class UsuarioListener {

    @PrePersist
    public void setCreated(Usuario u) {
        u.setFechaCreacion(new Date());
    }

    @PreUpdate
    public void setUpdated(Usuario u) {
        u.setFechaModificacion(new Date());
    }
}
