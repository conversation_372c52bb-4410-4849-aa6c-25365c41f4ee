/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.user.Usuario;
import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "MensajeriaDocumental", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "MensajeriaDocumental.findAll", query = "SELECT m FROM MensajeriaDocumental m")
    , @NamedQuery(name = "MensajeriaDocumental.findById", query = "SELECT m FROM MensajeriaDocumental m WHERE m.id = :id")
    , @NamedQuery(name = "MensajeriaDocumental.findByFechaProgramacion", query = "SELECT m FROM MensajeriaDocumental m WHERE m.fechaProgramacion = :fechaProgramacion")
    , @NamedQuery(name = "MensajeriaDocumental.findByFechaRecogida", query = "SELECT m FROM MensajeriaDocumental m WHERE m.fechaRecogida = :fechaRecogida")
    , @NamedQuery(name = "MensajeriaDocumental.findByFechaEntrega", query = "SELECT m FROM MensajeriaDocumental m WHERE m.fechaEntrega = :fechaEntrega")
    , @NamedQuery(name = "MensajeriaDocumental.findByFirmaRecogida", query = "SELECT m FROM MensajeriaDocumental m WHERE m.firmaRecogida = :firmaRecogida")
    , @NamedQuery(name = "MensajeriaDocumental.findByFirmaEntrega", query = "SELECT m FROM MensajeriaDocumental m WHERE m.firmaEntrega = :firmaEntrega")
    , @NamedQuery(name = "MensajeriaDocumental.findByEstado", query = "SELECT m FROM MensajeriaDocumental m WHERE m.estado = :estado")})
public class MensajeriaDocumental implements Serializable {

    @OneToMany(mappedBy = "idMensajeriaDocumental")
    private List<PlanillaDistribucionXMensajeriaDocumental> planillaDistribucionXMensajeriaDocumentalList;

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "FechaProgramacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaProgramacion;
    @Column(name = "FechaRecogida")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRecogida;
    @Column(name = "FechaEntrega")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaEntrega;
    @Column(name = "FirmaRecogida")
    private String firmaRecogida;
    @Column(name = "FirmaEntrega")
    private String firmaEntrega;
    @Column(name = "Estado")
    private Integer estado;
    @Column(name = "EstadoAnterior")
    private Integer estadoAnterior;
    @JoinColumn(name = "Documento", referencedColumnName = "Id")
    @ManyToOne
    private Documento documento;
    @JoinColumn(name = "Seccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccion;  
    @JoinColumn(name = "UsuarioEnvio", referencedColumnName = "Id")
    @ManyToOne
    private Usuario usuarioEnvio;
    @JoinColumn(name = "UsuarioRecibe", referencedColumnName = "Id")
    @ManyToOne
    private Usuario usuarioRecibe;

    public MensajeriaDocumental() {
    }

    public MensajeriaDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getFechaProgramacion() {
        return fechaProgramacion;
    }

    public void setFechaProgramacion(Date fechaProgramacion) {
        this.fechaProgramacion = fechaProgramacion;
    }

    public Date getFechaRecogida() {
        return fechaRecogida;
    }

    public void setFechaRecogida(Date fechaRecogida) {
        this.fechaRecogida = fechaRecogida;
    }

    public Date getFechaEntrega() {
        return fechaEntrega;
    }

    public void setFechaEntrega(Date fechaEntrega) {
        this.fechaEntrega = fechaEntrega;
    }

    public String getFirmaRecogida() {
        return firmaRecogida;
    }

    public void setFirmaRecogida(String firmaRecogida) {
        this.firmaRecogida = firmaRecogida;
    }

    public String getFirmaEntrega() {
        return firmaEntrega;
    }

    public void setFirmaEntrega(String firmaEntrega) {
        this.firmaEntrega = firmaEntrega;
    }

    public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }

    public Documento getDocumento() {
        return documento;
    }

    public void setDocumento(Documento documento) {
        this.documento = documento;
    }

    public SeccionSubSeccion getSeccion() {
        return seccion;
    }

    public void setSeccion(SeccionSubSeccion seccion) {
        this.seccion = seccion;
    }

    public Usuario getUsuarioEnvio() {
        return usuarioEnvio;
    }

    public void setUsuarioEnvio(Usuario usuarioEnvio) {
        this.usuarioEnvio = usuarioEnvio;
    }

    public Usuario getUsuarioRecibe() {
        return usuarioRecibe;
    }

    public void setUsuarioRecibe(Usuario usuarioRecibe) {
        this.usuarioRecibe = usuarioRecibe;
    }

    public Integer getEstadoAnterior() {
        return estadoAnterior;
    }

    public void setEstadoAnterior(Integer estadoAnterior) {
        this.estadoAnterior = estadoAnterior;
    }
    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof MensajeriaDocumental)) {
            return false;
        }
        MensajeriaDocumental other = (MensajeriaDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.MensajeriaDocumental[ id=" + id + " ]";
    }

    @XmlTransient
    @JsonIgnore
    public List<PlanillaDistribucionXMensajeriaDocumental> getPlanillaDistribucionXMensajeriaDocumentalList() {
        return planillaDistribucionXMensajeriaDocumentalList;
    }

    public void setPlanillaDistribucionXMensajeriaDocumentalList(List<PlanillaDistribucionXMensajeriaDocumental> planillaDistribucionXMensajeriaDocumentalList) {
        this.planillaDistribucionXMensajeriaDocumentalList = planillaDistribucionXMensajeriaDocumentalList;
    }
    
}
