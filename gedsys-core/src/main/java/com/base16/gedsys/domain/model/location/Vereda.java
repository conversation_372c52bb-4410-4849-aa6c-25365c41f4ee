package com.base16.gedsys.domain.model.location;

import com.base16.gedsys.domain.model.user.Usuario;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.document.Documento;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "vereda", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
    @NamedQuery(name = "Vereda.findAll", query = "SELECT v FROM Vereda v")
    , @NamedQuery(name = "Vereda.findById", query = "SELECT v FROM Vereda v WHERE v.id = :id")
    , @NamedQuery(name = "Vereda.findByBorrado", query = "SELECT v FROM Vereda v WHERE v.borrado = :borrado")
    , @NamedQuery(name = "Vereda.findByCodigo", query = "SELECT v FROM Vereda v WHERE v.codigo = :codigo")
    , @NamedQuery(name = "Vereda.findByMunicipioYcodigo", query = "SELECT v FROM Vereda v WHERE v.codigo = :codigo and v.municipio = :municipio")
    , @NamedQuery(name = "Vereda.findByFechaCreacion", query = "SELECT v FROM Vereda v WHERE v.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Vereda.findByFechaModificacion", query = "SELECT v FROM Vereda v WHERE v.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Vereda.findByNombre", query = "SELECT v FROM Vereda v WHERE v.nombre = :nombre")
    , @NamedQuery(name = "Vereda.buscarpormunicipio", query = "SELECT v FROM Vereda v WHERE v.municipio = :munic")
})
public class Vereda implements Serializable {

    @OneToMany(mappedBy = "vereda")
    private List<Documento> documentoList;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "Codigo")
    private String codigo;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "deleted")
    private Boolean deleted;
    @JoinColumn(name = "Municipio", referencedColumnName = "Id")
    @ManyToOne
    private Municipio municipio;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    public Vereda() {
    }

    public Vereda(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Municipio getMunicipio() {
        return municipio;
    }

    public void setMunicipio(Municipio municipio) {
        this.municipio = municipio;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Vereda)) {
            return false;
        }
        Vereda other = (Vereda) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Vereda[ id=" + id + " ]";
    }

    @XmlTransient
    @JsonIgnore
    public List<Documento> getDocumentoList() {
        return documentoList;
    }

    public void setDocumentoList(List<Documento> documentoList) {
        this.documentoList = documentoList;
    }

}
