/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.documentProducer.*;
import com.base16.gedsys.domain.model.entityListeners.AnexosListener;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "anexos", schema = "dbo")
@XmlRootElement
@EntityListeners(AnexosListener.class)
@NamedQueries({
        @NamedQuery(name = "Anexos.findAll", query = "SELECT a FROM Anexos a")
        , @NamedQuery(name = "Anexos.findById", query = "SELECT a FROM Anexos a WHERE a.id = :id")
        , @NamedQuery(name = "Anexos.findByDocumento", query = "SELECT a FROM Anexos a WHERE a.documento = :documento")
        , @NamedQuery(name = "Anexos.findByCarta", query = "SELECT a FROM Anexos a WHERE a.carta.id = :idCarta")
        , @NamedQuery(name = "Anexos.findByComunicacion", query = "SELECT a FROM Anexos a WHERE a.comunicacion.id = :idComunicacion")
        , @NamedQuery(name = "Anexos.findByResolucion", query = "SELECT a FROM Anexos a WHERE a.resolucion.id = :idResolucion")
        , @NamedQuery(name = "Anexos.findByActa", query = "SELECT a FROM Anexos a WHERE a.acta.id = :idActa")
        , @NamedQuery(name = "Anexos.findByCertificado", query = "SELECT a FROM Anexos a WHERE a.certificado.id = :idCertificado")
        , @NamedQuery(name = "Anexos.findByCircular", query = "SELECT a FROM Anexos a WHERE a.circular.id = :idCircular")
        , @NamedQuery(name = "Anexos.findByConstancia", query = "SELECT a FROM Anexos a WHERE a.constancia.id = :idConstancia")
        , @NamedQuery(name = "Anexos.findByInforme", query = "SELECT a FROM Anexos a WHERE a.informe.id = :idInforme")
        , @NamedQuery(name = "Anexos.findByFormato", query = "SELECT a FROM Anexos a WHERE a.formato = :formato")
        , @NamedQuery(name = "Anexos.findByFechaCreacion", query = "SELECT a FROM Anexos a WHERE a.fechaCreacion = :fechaCreacion")})
public class Anexos implements Serializable {


    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "nombreAnexo")
    private String nombreAnexo;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "rutaAnexo")
    private String rutaAnexo;
    @Size(max = 30)
    @Column(name = "formato")
    private String formato;
    @Column(name = "fechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @JoinColumn(name = "carta", referencedColumnName = "Id")
    @ManyToOne
    private Carta carta;
    @JoinColumn(name = "comunicacion", referencedColumnName = "Id")
    @ManyToOne
    private Comunicacion comunicacion;
    @JoinColumn(name = "acta", referencedColumnName = "Id")
    @ManyToOne
    private Acta acta;
    @JoinColumn(name = "certificado", referencedColumnName = "Id")
    @ManyToOne
    private Certificado certificado;
    @JoinColumn(name = "circular", referencedColumnName = "Id")
    @ManyToOne
    private Circular circular;
    @JoinColumn(name = "constancia", referencedColumnName = "Id")
    @ManyToOne
    private Constancia constancia;
    @JoinColumn(name = "informe", referencedColumnName = "Id")
    @ManyToOne
    private Informe informe;
    @JoinColumn(name = "resolucion", referencedColumnName = "Id")
    @ManyToOne
    private Resolucion resolucion;
    @JoinColumn(name = "documento", referencedColumnName = "Id")
    @ManyToOne
    private Documento documento;

    @Column(name = "esDocumentoAnexado")
    private Boolean esDocumentoAnexado;

    @Column(name = "documentEnvio")
    private Boolean documentEnvio;

    @Column(name = "documentRecepcion")
    private Boolean documentRecepcion;

    public Boolean getDocumentRecepcion() {
        return documentRecepcion;
    }

    public void setDocumentRecepcion(Boolean documentRecepcion) {
        this.documentRecepcion = documentRecepcion;
    }

    public Anexos() {
    }

    public Anexos(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNombreAnexo() {
        return nombreAnexo;
    }

    public void setNombreAnexo(String nombreAnexo) {
        this.nombreAnexo = nombreAnexo;
    }

    public String getRutaAnexo() {
        return rutaAnexo;
    }

    public void setRutaAnexo(String rutaAnexo) {
        this.rutaAnexo = rutaAnexo;
    }

    public String getFormato() {
        return formato;
    }

    public void setFormato(String formato) {
        this.formato = formato;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Carta getCarta() {
        return carta;
    }

    public void setCarta(Carta carta) {
        this.carta = carta;
    }

    public Comunicacion getComunicacion() {
        return comunicacion;
    }

    public void setComunicacion(Comunicacion comunicacion) {
        this.comunicacion = comunicacion;
    }

    public Resolucion getResolucion() {
        return resolucion;
    }

    public void setResolucion(Resolucion resolucion) {
        this.resolucion = resolucion;
    }

    public Documento getDocumento() {
        return documento;
    }

    public void setDocumento(Documento documento) {
        this.documento = documento;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Anexos)) {
            return false;
        }
        Anexos other = (Anexos) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.document.Anexos[ id=" + id + " ]";
    }

    public Boolean getEsDocumentoAnexado() {
        return esDocumentoAnexado;
    }

    public void setEsDocumentoAnexado(Boolean esDocumentoAnexado) {
        this.esDocumentoAnexado = esDocumentoAnexado;
    }

    public Boolean getDocumentEnvio() {
        return documentEnvio;
    }

    public void setDocumentEnvio(Boolean documentEnvio) {
        this.documentEnvio = documentEnvio;
    }

    public Acta getActa() {
        return acta;
    }

    public void setActa(Acta acta) {
        this.acta = acta;
    }

    public Certificado getCertificado() {
        return certificado;
    }

    public void setCertificado(Certificado certificado) {
        this.certificado = certificado;
    }

    public Circular getCircular() {
        return circular;
    }

    public void setCircular(Circular circular) {
        this.circular = circular;
    }

    public Constancia getConstancia() {
        return constancia;
    }

    public void setConstancia(Constancia constancia) {
        this.constancia = constancia;
    }

    public Informe getInforme() {
        return informe;
    }

    public void setInforme(Informe informe) {
        this.informe = informe;
    }

}
