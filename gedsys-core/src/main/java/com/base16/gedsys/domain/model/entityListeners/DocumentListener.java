package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.document.Documento;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;

public class DocumentListener {
    @PrePersist
    public void onCreate(Documento d){
        d.setFechaCreacion(new Date());
    }

    @PreUpdate
    public void onUpdate(Documento d){
        d.setFechaModificacion(new Date());
    }
}
