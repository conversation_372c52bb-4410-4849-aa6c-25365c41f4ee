/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "formatos", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Formatos.findAll", query = "SELECT f FROM Formatos f")
    , @NamedQuery(name = "Formatos.findById", query = "SELECT f FROM Formatos f WHERE f.id = :id")
    , @NamedQuery(name = "Formatos.findByBorrado", query = "SELECT f FROM Formatos f WHERE f.borrado = :borrado")
    , @NamedQuery(name = "Formatos.findByEstado", query = "SELECT f FROM Formatos f WHERE f.estado = :estado")
    , @NamedQuery(name = "Formatos.findByFechaCreacion", query = "SELECT f FROM Formatos f WHERE f.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Formatos.findByFechaModificacion", query = "SELECT f FROM Formatos f WHERE f.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Formatos.findByNombre", query = "SELECT f FROM Formatos f WHERE f.nombre = :nombre")
    , @NamedQuery(name = "Formatos.findByFormulario", query = "SELECT f FROM Formatos f WHERE f.formulario = :formulario")
    , @NamedQuery(name = "Formatos.findByPredeterminado", query = "SELECT f FROM Formatos f WHERE f.predeterminado = :predeterminado")})
public class Formatos implements Serializable {

    @Column(name = "isExtern")
    private Boolean isExtern;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Basic(optional = false)
    @NotNull
    @Column(name = "Borrado")
    private boolean borrado;
    @Column(name = "Estado")
    private Boolean estado;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Size(max = 255)
    @Column(name = "Nombre")
    private String nombre;
    @Size(max = 255)
    @Column(name = "Formulario")
    private String formulario;
    @Column(name = "Predeterminado")
    private Boolean predeterminado;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    public Formatos() {
    }

    public Formatos(Integer id) {
        this.id = id;
    }

    public Formatos(Integer id, boolean borrado) {
        this.id = id;
        this.borrado = borrado;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(boolean borrado) {
        this.borrado = borrado;
    }

    public Boolean getEstado() {
        return estado;
    }

    public void setEstado(Boolean estado) {
        this.estado = estado;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getFormulario() {
        return formulario;
    }

    public void setFormulario(String formulario) {
        this.formulario = formulario;
    }

    public Boolean getPredeterminado() {
        return predeterminado;
    }

    public void setPredeterminado(Boolean predeterminado) {
        this.predeterminado = predeterminado;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Formatos)) {
            return false;
        }
        Formatos other = (Formatos) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.documentProducer.Formatos[ id=" + id + " ]";
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }
    
}
