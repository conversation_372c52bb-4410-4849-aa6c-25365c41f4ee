/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "destinatariosdoc", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "DestinatariosDoc.findAll", query = "SELECT d FROM DestinatariosDocumento d")
        , @NamedQuery(name = "DestinatariosDoc.findById", query = "SELECT d FROM DestinatariosDocumento d WHERE d.id = :id")
        , @NamedQuery(name = "DestinatariosDoc.findByFechaCreacion", query = "SELECT d FROM DestinatariosDocumento d WHERE d.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "DestinatariosDoc.findByFechaModificacion", query = "SELECT d FROM DestinatariosDocumento d WHERE d.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "DestinatariosDoc.findByPrincipal", query = "SELECT d FROM DestinatariosDocumento d WHERE d.principal = :principal")
        , @NamedQuery(name = "DestinatariosDoc.buscarPorDocumento", query = "SELECT d FROM DestinatariosDocumento d WHERE d.documentoId = :documento")
        , @NamedQuery(name = "DestinatariosDoc.buscarPorListaDoc", query = "SELECT d FROM DestinatariosDocumento d WHERE d.documentoId IN :documentos")
        , @NamedQuery(name = "DestinatariosDoc.buscarPorDestDoc", query = "SELECT d FROM DestinatariosDocumento d WHERE d.documentoId = :documento AND d.destinatarioId = :destinatario")
        , @NamedQuery(name = "DestinatariosDoc.findDesDocUsu", query = "SELECT d FROM DestinatariosDocumento d WHERE d.documentoId = :documento AND d.destinatarioId = :destinatario AND d.creadoPor = :usuario")})


public class DestinatariosDocumento implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Long id;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Principal")
    private Short principal;
    @JoinColumn(name = "Documento_Id", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Documento documentoId;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario modificadoPor;
    @JoinColumn(name = "DestinatarioId", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario destinatarioId;
    @Column(name = "Negrilla")
    private Boolean negrilla;
    @Column(name = "IdProduccionDoc")
    private Integer idProduccionDoc;
    @Column(name = "Tipo")
    private String tipo;
    @Column(name = "Estado")
    private Boolean estado;

    public DestinatariosDocumento() {
    }

    public DestinatariosDocumento(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Short getPrincipal() {
        return principal;
    }

    public void setPrincipal(Short principal) {
        this.principal = principal;
    }

    public Documento getDocumentoId() {
        return documentoId;
    }

    public void setDocumentoId(Documento documentoId) {
        this.documentoId = documentoId;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Usuario getDestinatarioId() {
        return destinatarioId;
    }

    public void setDestinatarioId(Usuario destinatarioId) {
        this.destinatarioId = destinatarioId;
    }

    public Boolean getNegrilla() {
        return negrilla;
    }

    public void setNegrilla(Boolean negrilla) {
        this.negrilla = negrilla;
    }

    public Integer getIdProduccionDoc() {
        return idProduccionDoc;
    }

    public void setIdProduccionDoc(Integer idProduccionDoc) {
        this.idProduccionDoc = idProduccionDoc;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Boolean getEstado() {
        return estado;
    }

    public void setEstado(Boolean estado) {
        this.estado = estado;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof DestinatariosDocumento)) {
            return false;
        }
        DestinatariosDocumento other = (DestinatariosDocumento) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.sucomunicacion.gedsys.entities.DestinatariosDoc[ id=" + id + " ]";
    }

}
