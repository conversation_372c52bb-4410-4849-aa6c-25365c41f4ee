/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "ComentarioBusquedaRadicado")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "ComentarioBusquedaRadicado.findAll", query = "SELECT c FROM ComentarioBusquedaRadicado c")
    , @NamedQuery(name = "ComentarioBusquedaRadicado.findById", query = "SELECT c FROM ComentarioBusquedaRadicado c WHERE c.id = :id")
    , @NamedQuery(name = "ComentarioBusquedaRadicado.findByComentario", query = "SELECT c FROM ComentarioBusquedaRadicado c WHERE c.comentario = :comentario")
    , @NamedQuery(name = "ComentarioBusquedaRadicado.findByFechaCreacion", query = "SELECT c FROM ComentarioBusquedaRadicado c WHERE c.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "ComentarioBusquedaRadicado.findByFechaModificacion", query = "SELECT c FROM ComentarioBusquedaRadicado c WHERE c.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "ComentarioBusquedaRadicado.findByConsecutivoUsuario", query = "SELECT c FROM ComentarioBusquedaRadicado c WHERE c.consecutivo =:radicado")
})
public class ComentarioBusquedaRadicado implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Size(max = 250)
    @Column(name = "Comentario")
    private String comentario;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @JoinColumn(name = "ConsecutivoUsuario", referencedColumnName = "Id")
    @ManyToOne
    private ConsecutivosUsuario consecutivoUsuario;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @Size(max = 50)
    @Column(name = "Consecutivo")
    private String consecutivo;

    public ComentarioBusquedaRadicado() {
    }

    public ComentarioBusquedaRadicado(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public ConsecutivosUsuario getConsecutivoUsuario() {
        return consecutivoUsuario;
    }

    public void setConsecutivoUsuario(ConsecutivosUsuario consecutivoUsuario) {
        this.consecutivoUsuario = consecutivoUsuario;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof ComentarioBusquedaRadicado)) {
            return false;
        }
        ComentarioBusquedaRadicado other = (ComentarioBusquedaRadicado) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.document.ComentarioBusquedaRadicado[ id=" + id + " ]";
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

}
