package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;

public class IncidenciaGeneralListener {
    @PrePersist
    public void setCreated(IncidenciaGeneral ig){
        ig.setFechaCreacion(new Date());
        ig.setDeleted(false);
    }

    @PreUpdate
    public void setUpdated(IncidenciaGeneral ig){
        ig.setFechaModificacion(new Date());
    }
}
