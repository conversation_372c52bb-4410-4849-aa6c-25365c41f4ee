package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.domain.model.archive.TipoDocumental;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;
import java.util.Optional;

public class TipoDocumentalListener {
    @PrePersist
    public void onCreate(TipoDocumental t) {
        t.setFechaCreacion(new Date());
        t.setEsPQRSF(Optional.ofNullable(t.getEsPQRSF()).isPresent() ? t.getEsPQRSF() : false);
        t.setTipoIncidencia(Optional.ofNullable(t.getTipoIncidencia()).isPresent() ? t.getTipoIncidencia() : "");
    }
    @PreUpdate
    public void onUpdate(TipoDocumental t){
        t.setFechaModificacion(new Date());
        t.setEsPQRSF(Optional.ofNullable(t.getEsPQRSF()).isPresent() ? t.getEsPQRSF() : false);
        t.setTipoIncidencia(Optional.ofNullable(t.getTipoIncidencia()).isPresent() ? t.getTipoIncidencia() : "");
    }
}
