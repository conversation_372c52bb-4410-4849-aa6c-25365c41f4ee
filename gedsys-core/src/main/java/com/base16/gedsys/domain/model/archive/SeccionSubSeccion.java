/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import com.base16.gedsys.domain.model.documentProducer.Comunicacion;
import com.base16.gedsys.domain.model.documentProducer.Circular;
import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.documentProducer.Acta;
import com.base16.gedsys.domain.model.documentProducer.Certificado;
import com.base16.gedsys.domain.model.document.Consecutivo;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.documentProducer.Constancia;
import com.base16.gedsys.domain.model.documentProducer.Informe;
import com.base16.gedsys.domain.model.document.MensajeriaDocumental;
import com.base16.gedsys.domain.model.document.PlanillaDistribucionDocumental;
import com.base16.gedsys.domain.model.documentProducer.Resolucion;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaEspecifica;
import com.base16.gedsys.domain.model.user.Usuario;
import com.base16.gedsys.domain.model.user.UsuarioSeccion;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "seccionsubseccion", catalog = "", schema = "")
@XmlRootElement
@AdditionalCriteria("this.deleted = false")
@NamedQueries({
    @NamedQuery(name = "SeccionSubSeccion.findAll", query = "SELECT s FROM SeccionSubSeccion s")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findById", query = "SELECT s FROM SeccionSubSeccion s WHERE s.id = :id AND s.estado=true")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByBorrado", query = "SELECT s FROM SeccionSubSeccion s WHERE s.borrado = :borrado")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByCodigo", query = "SELECT s FROM SeccionSubSeccion s WHERE s.codigo = :codigo")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByOrderCodigo", query = "SELECT s FROM SeccionSubSeccion s ORDER BY s.codigo ASC")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByFechaCreacion", query = "SELECT s FROM SeccionSubSeccion s WHERE s.fechaCreacion = :fechaCreacion")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByFechaModificacion", query = "SELECT s FROM SeccionSubSeccion s WHERE s.fechaModificacion = :fechaModificacion")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByNombre", query = "SELECT s FROM SeccionSubSeccion s WHERE s.nombre = :nombre")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByDependeDe", query = "SELECT s FROM SeccionSubSeccion s WHERE s.dependeDe = :dependeDe AND s.estado=true")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findRoots", query = "SELECT s FROM SeccionSubSeccion s WHERE s.dependeDe is null AND s.estado=true")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findSeccionResponsable", query = "SELECT s FROM SeccionSubSeccion s WHERE s.responsable = :responsable and s.seccionSubSeccionCollection = :seccion")
    ,
    @NamedQuery(name = "SeccionSubSeccion.findByListSecciones", query = "SELECT s FROM SeccionSubSeccion s WHERE (s.id IN :seccion)")
})
public class SeccionSubSeccion implements Serializable {

    
 
    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Resolucion> resolucionList;

    @OneToMany(mappedBy = "seccion")
    private List<PlanillaDistribucionDocumental> planillaDistribucionDocumentalList;

    @OneToMany(mappedBy = "seccion")
    private List<IncidenciaEspecifica> incidenciaEspecificaList;

    @OneToMany(mappedBy = "seccion")
    private List<UsuarioSeccion> usuarioSeccionList;

    @OneToMany(mappedBy = "seccion")
    private List<SubSeriesTiposDocumentales> subSeriesTiposDocumentalesList;

    @OneToMany(mappedBy = "seccion")
    private List<Documento> documentoList;

    @OneToMany(mappedBy = "seccionSubseccion")
    private List<Consecutivo> consecutivoList;

    @OneToMany(mappedBy = "subseccion")
    private List<Subseccionsubserie> subseccionsubserieList;

    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Constancia> constanciaList;

    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Carta> cartaList;
    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Acta> actaList;
    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Informe> informeList;
    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Circular> circularList;
    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Certificado> certificadoList;
    @OneToMany(mappedBy = "seccionSubSeccion")
    private List<Comunicacion> comunicacionList;

    @OneToMany(mappedBy = "seccion")
    private List<MensajeriaDocumental> mensajeriaDocumentalList;

    @OneToMany(mappedBy = "seccion")
    private List<Usuario> usuarioList;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Column(name = "Borrado")
    private Boolean borrado;
    @Column(name = "Codigo")
    private String codigo;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @Column(name = "Nombre")
    private String nombre;
    @Column(name = "Estado")
    private Boolean estado;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;
    @OneToMany(mappedBy = "dependeDe")
    private Collection<SeccionSubSeccion> seccionSubSeccionCollection;
    @JoinColumn(name = "DependeDe", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion dependeDe;
    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;
    @OneToMany(mappedBy = "subSeccion")
    private List<UnidadDocumental> unidadDocumentalList;
    @Column(name = "deleted")
    private Boolean deleted;

    public SeccionSubSeccion() {
    }

    public SeccionSubSeccion(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getBorrado() {
        return borrado;
    }

    public void setBorrado(Boolean borrado) {
        this.borrado = borrado;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Boolean getEstado() {return estado;}

    public void setEstado(Boolean estado) {this.estado = estado;}

    public Boolean getDeleted() { return deleted; }

    public void setDeleted(Boolean deleted) { this.deleted = deleted; }

    @XmlTransient
    @JsonIgnore
    public Collection<SeccionSubSeccion> getSeccionSubSeccionCollection() {
        return seccionSubSeccionCollection;
    }

    public void setSeccionSubSeccionCollection(Collection<SeccionSubSeccion> seccionSubSeccionCollection) {
        this.seccionSubSeccionCollection = seccionSubSeccionCollection;
    }

    public SeccionSubSeccion getDependeDe() {
        return dependeDe;
    }

    public void setDependeDe(SeccionSubSeccion dependeDe) {
        this.dependeDe = dependeDe;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SeccionSubSeccion)) {
            return false;
        }
        SeccionSubSeccion other = (SeccionSubSeccion) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return this.nombre;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    @XmlTransient
    @JsonIgnore
    public List<UnidadDocumental> getUnidadDocumentalList() {
        return unidadDocumentalList;
    }

    public void setUnidadDocumentalList(List<UnidadDocumental> unidadDocumentalList) {
        this.unidadDocumentalList = unidadDocumentalList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Usuario> getUsuarioList() {
        return usuarioList;
    }

    public void setUsuarioList(List<Usuario> usuarioList) {
        this.usuarioList = usuarioList;
    }

    @XmlTransient
    @JsonIgnore
    public List<MensajeriaDocumental> getMensajeriaDocumentalList() {
        return mensajeriaDocumentalList;
    }

    public void setMensajeriaDocumentalList(List<MensajeriaDocumental> mensajeriaDocumentalList) {
        this.mensajeriaDocumentalList = mensajeriaDocumentalList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Carta> getCartaList() {
        return cartaList;
    }

    public void setCartaList(List<Carta> cartaList) {
        this.cartaList = cartaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Acta> getActaList() {
        return actaList;
    }

    public void setActaList(List<Acta> actaList) {
        this.actaList = actaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Informe> getInformeList() {
        return informeList;
    }

    public void setInformeList(List<Informe> informeList) {
        this.informeList = informeList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Circular> getCircularList() {
        return circularList;
    }

    public void setCircularList(List<Circular> circularList) {
        this.circularList = circularList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Certificado> getCertificadoList() {
        return certificadoList;
    }

    public void setCertificadoList(List<Certificado> certificadoList) {
        this.certificadoList = certificadoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Comunicacion> getComunicacionList() {
        return comunicacionList;
    }

    public void setComunicacionList(List<Comunicacion> comunicacionList) {
        this.comunicacionList = comunicacionList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Constancia> getConstanciaList() {
        return constanciaList;
    }

    public void setConstanciaList(List<Constancia> constanciaList) {
        this.constanciaList = constanciaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Subseccionsubserie> getSubseccionsubserieList() {
        return subseccionsubserieList;
    }

    public void setSubseccionsubserieList(List<Subseccionsubserie> subseccionsubserieList) {
        this.subseccionsubserieList = subseccionsubserieList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Consecutivo> getConsecutivoList() {
        return consecutivoList;
    }

    public void setConsecutivoList(List<Consecutivo> consecutivoList) {
        this.consecutivoList = consecutivoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<Documento> getDocumentoList() {
        return documentoList;
    }

    public void setDocumentoList(List<Documento> documentoList) {
        this.documentoList = documentoList;
    }

    @XmlTransient
    @JsonIgnore
    public List<SubSeriesTiposDocumentales> getSubSeriesTiposDocumentalesList() {
        return subSeriesTiposDocumentalesList;
    }

    public void setSubSeriesTiposDocumentalesList(List<SubSeriesTiposDocumentales> subSeriesTiposDocumentalesList) {
        this.subSeriesTiposDocumentalesList = subSeriesTiposDocumentalesList;
    }

    @XmlTransient
    @JsonIgnore
    public List<UsuarioSeccion> getUsuarioSeccionList() {
        return usuarioSeccionList;
    }

    public void setUsuarioSeccionList(List<UsuarioSeccion> usuarioSeccionList) {
        this.usuarioSeccionList = usuarioSeccionList;
    }

    @XmlTransient
    @JsonIgnore
    public List<IncidenciaEspecifica> getIncidenciaEspecificaList() {
        return incidenciaEspecificaList;
    }

    public void setIncidenciaEspecificaList(List<IncidenciaEspecifica> incidenciaEspecificaList) {
        this.incidenciaEspecificaList = incidenciaEspecificaList;
    }

    @XmlTransient
    @JsonIgnore
    public List<PlanillaDistribucionDocumental> getPlanillaDistribucionDocumentalList() {
        return planillaDistribucionDocumentalList;
    }

    public void setPlanillaDistribucionDocumentalList(List<PlanillaDistribucionDocumental> planillaDistribucionDocumentalList) {
        this.planillaDistribucionDocumentalList = planillaDistribucionDocumentalList;
    }


    @XmlTransient
    @JsonIgnore
    public List<Resolucion> getResolucionList() {
        return resolucionList;
    }

    public void setResolucionList(List<Resolucion> resolucionList) {
        this.resolucionList = resolucionList;
    }

   
}
