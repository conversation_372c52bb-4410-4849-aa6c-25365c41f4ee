/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "subseccionsubserie", catalog = "", schema = "dbo")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Subseccionsubserie.findAll", query = "SELECT s FROM Subseccionsubserie s")
    , @NamedQuery(name = "Subseccionsubserie.findById", query = "SELECT s FROM Subseccionsubserie s WHERE s.id = :id")
    , @NamedQuery(name = "Subseccionsubserie.findByCreadoPor", query = "SELECT s FROM Subseccionsubserie s WHERE s.creadoPor = :creadoPor")
    , @NamedQuery(name = "Subseccionsubserie.findByModificadoPor", query = "SELECT s FROM Subseccionsubserie s WHERE s.modificadoPor = :modificadoPor")
    , @NamedQuery(name = "Subseccionsubserie.findByFechaCreacion", query = "SELECT s FROM Subseccionsubserie s WHERE s.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "Subseccionsubserie.findByFechaModificacion", query = "SELECT s FROM Subseccionsubserie s WHERE s.fechaModificacion = :fechaModificacion")
    , @NamedQuery(name = "Subseccionsubserie.findBySeccionSubSeccion", query = "SELECT DISTINCT sbsb FROM Subseccionsubserie sbsb WHERE sbsb.subserie = :SubSerie")
    , @NamedQuery(name = "Subseccionsubserie.findBySubSeccion", query = "SELECT s FROM Subseccionsubserie s WHERE s.subseccion = :seccion")})
public class Subseccionsubserie implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "CreadoPor")
    private Integer creadoPor;
    @Column(name = "ModificadoPor")
    private Integer modificadoPor;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @JoinColumn(name = "subseccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion subseccion;
    @JoinColumn(name = "subserie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subserie;

    public Subseccionsubserie() {
    }

    public Subseccionsubserie(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Integer creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Integer getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Integer modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public SeccionSubSeccion getSubseccion() {
        return subseccion;
    }

    public void setSubseccion(SeccionSubSeccion subseccion) {
        this.subseccion = subseccion;
    }

    public SubSerie getSubserie() {
        return subserie;
    }

    public void setSubserie(SubSerie subserie) {
        this.subserie = subserie;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Subseccionsubserie)) {
            return false;
        }
        Subseccionsubserie other = (Subseccionsubserie) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Subseccionsubserie[ id=" + id + " ]";
    }
    
}
