package com.base16.gedsys.domain.model.entityListeners;

import com.base16.gedsys.application.Configuracion;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Date;

public class ConfiguracionListener {

    @PrePersist
    public void setCreated(Configuracion u) {
        u.setFechaCreacion(new Date());
    }

    @PreUpdate
    public void setUpdated(Configuracion u) {
        u.setFechaModificacion(new Date());
    }
}
