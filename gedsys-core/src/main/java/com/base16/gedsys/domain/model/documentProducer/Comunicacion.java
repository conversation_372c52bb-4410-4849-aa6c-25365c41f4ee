/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.documentProducer;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.archive.Serie;
import com.base16.gedsys.domain.model.archive.SubSerie;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.user.Usuario;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.codehaus.jackson.annotate.JsonIgnore;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "comunicacion", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Comunicacion.findAll", query = "SELECT c FROM Comunicacion c")
    ,
        @NamedQuery(name = "Comunicacion.findById", query = "SELECT c FROM Comunicacion c WHERE c.id = :id")
    ,
        @NamedQuery(name = "Comunicacion.findByAsunto", query = "SELECT c FROM Comunicacion c WHERE c.asunto = :asunto")
    ,
        @NamedQuery(name = "Comunicacion.findByDescripcionAnexos", query = "SELECT c FROM Comunicacion c WHERE c.descripcionAnexos = :descripcionAnexos")
    ,
        @NamedQuery(name = "Comunicacion.findByFechaCreacion", query = "SELECT c FROM Comunicacion c WHERE c.fechaCreacion = :fechaCreacion")
    ,
        @NamedQuery(name = "Comunicacion.findByFechaModificacion", query = "SELECT c FROM Comunicacion c WHERE c.fechaModificacion = :fechaModificacion")
    ,
        @NamedQuery(name = "Comunicacion.findByFechaFirma", query = "SELECT c FROM Comunicacion c WHERE c.fechaFirma = :fechaFirma")
    ,
        @NamedQuery(name = "Comunicacion.findByDocumentoPadre", query = "SELECT c FROM Comunicacion c WHERE c.documentoPadre = :documentoPadre")
    ,
        @NamedQuery(name = "Comunicacion.findByEstadoYUsuario", query = "SELECT c FROM Comunicacion c WHERE c.estado = :estado AND (c.remitente = :usuario OR c.creadoPor = :creadoPor)")
    ,
        @NamedQuery(name = "Comunicacion.findByEstado", query = "SELECT c FROM Comunicacion c WHERE c.estado = :estado")
    ,
        @NamedQuery(name = "Comunicacion.findByDocumentoFirmado", query = "SELECT c FROM Comunicacion c WHERE c.documentoFirmado = :documentoFirmado")})
public class Comunicacion implements Serializable {

    @Column(name = "active")
    private Boolean active;

    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne
    private Usuario responsable;
    @OneToMany(mappedBy = "comunicacion")
    private List<ComunicacionRemInterno> comunicacionRemInternoList;

    @Column(name = "isExtern")
    private Boolean isExtern;

    @Column(name = "fechaDocumento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaDocumento;

    @JoinColumn(name = "ciudad", referencedColumnName = "Id")
    @ManyToOne
    private Municipio ciudad;

    @Size(max = 255)
    @Column(name = "CargoFirmaUno")
    private String cargoFirmaUno;

    @Lob
    @Column(name = "comentario")
    private String comentario;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "ConCopiaExterna")
    private String conCopiaExterna;

    @JoinColumn(name = "quienProyecto", referencedColumnName = "Id")
    @ManyToOne
    private Usuario quienProyecto;

    @Column(name = "FechaFirma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;
    @Size(max = 50)
    @Column(name = "Estado")
    private String estado;

    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;
    @Column(name = "TipoComunicacion")
    private String tipoComunicacion;
    @JoinColumn(name = "SeccionSubSeccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccionSubSeccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "comunicacion")
    private Collection<ComunicacionConCopia> comunicacionccCollection;

    @Column(name = "Despedida")
    private String despedida;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "Asunto")
    private String asunto;
    @Column(name = "Consecutivo")
    private String consecutivo;
    @Lob
    @Column(name = "Contenido")
    private String contenido;
    @Column(name = "DescripcionAnexos")
    private String descripcionAnexos;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "comunicacion")
    private List<ComunicacionConCopia> comunicacionccList;
    @JoinColumn(name = "Destinatario", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario destinatario;
    @JoinColumn(name = "Remitente", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario remitente;
    @JoinColumn(name = "CreadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario creadoPor;
    @JoinColumn(name = "ModificadoPor", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Usuario modificadoPor;
    @JoinColumn(name = "documentoPadre", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Documento documentoPadre;

    @JoinColumn(name = "formatoBase", referencedColumnName = "Id")
    @ManyToOne(optional = false)
    private Formatos formatoBase;

    @JoinColumn(name = "DocumentoFirmado", referencedColumnName = "Id")
    @ManyToOne
    private Documento documentoFirmado;

    public Comunicacion() {
    }

    public Comunicacion(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getContenido() {
        return contenido;
    }

    public void setContenido(String contenido) {
        this.contenido = contenido;
    }

    public String getDescripcionAnexos() {
        return descripcionAnexos;
    }

    public void setDescripcionAnexos(String descripcionAnexos) {
        this.descripcionAnexos = descripcionAnexos;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getConsecutivo() {
        return consecutivo;
    }

    public void setConsecutivo(String consecutivo) {
        this.consecutivo = consecutivo;
    }

    @XmlTransient
    @JsonIgnore
    public List<ComunicacionConCopia> getComunicacionccList() {
        return comunicacionccList;
    }

    public void setComunicacionccList(List<ComunicacionConCopia> comunicacionccList) {
        this.comunicacionccList = comunicacionccList;
    }

    public Usuario getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(Usuario destinatario) {
        this.destinatario = destinatario;
    }

    public Usuario getRemitente() {
        return remitente;
    }

    public void setRemitente(Usuario remitente) {
        this.remitente = remitente;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    @XmlTransient
    @JsonIgnore
    public Collection<ComunicacionConCopia> getComunicacionccCollection() {
        return comunicacionccCollection;
    }

    public void setComunicacionccCollection(Collection<ComunicacionConCopia> comunicacionccCollection) {
        this.comunicacionccCollection = comunicacionccCollection;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Comunicacion)) {
            return false;
        }
        Comunicacion other = (Comunicacion) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.Comunicacion[ id=" + id + " ]";
    }

    public String getDespedida() {
        return despedida;
    }

    public void setDespedida(String despedida) {
        this.despedida = despedida;
    }

    public String getTipoComunicacion() {
        return tipoComunicacion;
    }

    public void setTipoComunicacion(String tipoComunicacion) {
        this.tipoComunicacion = tipoComunicacion;
    }

    public SeccionSubSeccion getSeccionSubSeccion() {
        return seccionSubSeccion;
    }

    public void setSeccionSubSeccion(SeccionSubSeccion seccionSubSeccion) {
        this.seccionSubSeccion = seccionSubSeccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public Usuario getQuienProyecto() {
        return quienProyecto;
    }

    public void setQuienProyecto(Usuario quienProyecto) {
        this.quienProyecto = quienProyecto;
    }

    public String getConCopiaExterna() {
        return conCopiaExterna;
    }

    public void setConCopiaExterna(String conCopiaExterna) {
        this.conCopiaExterna = conCopiaExterna;
    }

    public String getCargoFirmaUno() {
        return cargoFirmaUno;
    }

    public void setCargoFirmaUno(String cargoFirmaUno) {
        this.cargoFirmaUno = cargoFirmaUno;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Municipio getCiudad() {
        return ciudad;
    }

    public void setCiudad(Municipio ciudad) {
        this.ciudad = ciudad;
    }

    public Date getFechaDocumento() {
        return fechaDocumento;
    }

    public void setFechaDocumento(Date fechaDocumento) {
        this.fechaDocumento = fechaDocumento;
    }

    public Boolean getIsExtern() {
        return isExtern;
    }

    public void setIsExtern(Boolean isExtern) {
        this.isExtern = isExtern;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    public Documento getDocumentoPadre() {
        return documentoPadre;
    }

    public void setDocumentoPadre(Documento documentoPadre) {
        this.documentoPadre = documentoPadre;
    }

    public Formatos getFormatoBase() {
        return formatoBase;
    }

    public void setFormatoBase(Formatos formatoBase) {
        this.formatoBase = formatoBase;
    }

    @XmlTransient
    @JsonIgnore
    public List<ComunicacionRemInterno> getComunicacionRemInternoList() {
        return comunicacionRemInternoList;
    }

    public void setComunicacionRemInternoList(List<ComunicacionRemInterno> comunicacionRemInternoList) {
        this.comunicacionRemInternoList = comunicacionRemInternoList;
    }

    public Documento getDocumentoFirmado() {
        return documentoFirmado;
    }

    public void setDocumentoFirmado(Documento documentoFirmado) {
        this.documentoFirmado = documentoFirmado;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
