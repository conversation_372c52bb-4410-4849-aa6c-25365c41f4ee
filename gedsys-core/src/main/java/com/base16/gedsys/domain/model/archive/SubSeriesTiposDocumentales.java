/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.archive;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "subSeriesTiposDocumentales", catalog = "", schema = "dbo")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SubSeriesTiposDocumentales.findAll", query = "SELECT s FROM SubSeriesTiposDocumentales s")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findById", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.id = :id")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findByEstado", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.estado = :estado")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findByAnio", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.anio = :anio")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findByCreadoPor", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.creadoPor = :creadoPor")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findByModificadoPor", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.modificadoPor = :modificadoPor")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findByFechaCreacion", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "SubSeriesTiposDocumentales.findByFechaModificacion", query = "SELECT s FROM SubSeriesTiposDocumentales s WHERE s.fechaModificacion = :fechaModificacion")})

public class SubSeriesTiposDocumentales implements Serializable {

    @JoinColumn(name = "Seccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccion;
    @JoinColumn(name = "Serie", referencedColumnName = "Id")
    @ManyToOne
    private Serie serie;

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "Id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "Estado")
    private Boolean estado;
    @Column(name = "anio")
    private Integer anio;
    @Column(name = "CreadoPor")
    private Integer creadoPor;
    @Column(name = "ModificadoPor")
    private Integer modificadoPor;
    @Column(name = "FechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCreacion;
    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;
    @JoinColumn(name = "SubSerie", referencedColumnName = "Id")
    @ManyToOne
    private SubSerie subSerie;
    @JoinColumn(name = "TipoDocumental", referencedColumnName = "Id")
    @ManyToOne
    private TipoDocumental tipoDocumental;

    public SubSeriesTiposDocumentales() {
    }

    public SubSeriesTiposDocumentales(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getEstado() {
        return estado;
    }

    public void setEstado(Boolean estado) {
        this.estado = estado;
    }

    public Integer getAnio() {
        return anio;
    }

    public void setAnio(Integer anio) {
        this.anio = anio;
    }

    public Integer getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Integer creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Integer getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Integer modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public SubSerie getSubSerie() {
        return subSerie;
    }

    public void setSubSerie(SubSerie subSerie) {
        this.subSerie = subSerie;
    }

    public TipoDocumental getTipoDocumental() {
        return tipoDocumental;
    }

    public void setTipoDocumental(TipoDocumental tipoDocumental) {
        this.tipoDocumental = tipoDocumental;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SubSeriesTiposDocumentales)) {
            return false;
        }
        SubSeriesTiposDocumentales other = (SubSeriesTiposDocumentales) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.entities.SubSeriesTiposDocumentales[ id=" + id + " ]";
    }

    public SeccionSubSeccion getSeccion() {
        return seccion;
    }

    public void setSeccion(SeccionSubSeccion seccion) {
        this.seccion = seccion;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }
    
}
