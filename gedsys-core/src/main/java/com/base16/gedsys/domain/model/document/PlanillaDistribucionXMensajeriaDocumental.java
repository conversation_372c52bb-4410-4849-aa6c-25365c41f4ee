/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.document;

import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR> Programacion02
 */
@Entity
@Table(name = "PlanillaDistribucionXMensajeriaDocumental", catalog = "", schema = "")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "PlanillaDistribucionXMensajeriaDocumental.findAll", query = "SELECT p FROM PlanillaDistribucionXMensajeriaDocumental p")
    , @NamedQuery(name = "PlanillaDistribucionXMensajeriaDocumental.findById", query = "SELECT p FROM PlanillaDistribucionXMensajeriaDocumental p WHERE p.id = :id")
    , @NamedQuery(name = "PlanillaDistribucionXMensajeriaDocumental.findByEstado", query = "SELECT p FROM PlanillaDistribucionXMensajeriaDocumental p WHERE p.estado = :estado")})
public class PlanillaDistribucionXMensajeriaDocumental implements Serializable {

    @Lob
    @Size(max = 2147483647)
    @Column(name = "comentario")
    private String comentario;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    @Column(name = "estado")
    private Integer estado;
    @Column(name = "ordenPlanilla")
    private Integer ordenPlanilla;
    @JoinColumn(name = "idMensajeriaDocumental", referencedColumnName = "Id")
    @ManyToOne
    private MensajeriaDocumental idMensajeriaDocumental;
    @JoinColumn(name = "idPlanillaDistribucion", referencedColumnName = "id")
    @ManyToOne
    private PlanillaDistribucionDocumental idPlanillaDistribucion;

    public PlanillaDistribucionXMensajeriaDocumental() {
    }

    public PlanillaDistribucionXMensajeriaDocumental(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }

    public MensajeriaDocumental getIdMensajeriaDocumental() {
        return idMensajeriaDocumental;
    }

    public void setIdMensajeriaDocumental(MensajeriaDocumental idMensajeriaDocumental) {
        this.idMensajeriaDocumental = idMensajeriaDocumental;
    }

    public PlanillaDistribucionDocumental getIdPlanillaDistribucion() {
        return idPlanillaDistribucion;
    }

    public void setIdPlanillaDistribucion(PlanillaDistribucionDocumental idPlanillaDistribucion) {
        this.idPlanillaDistribucion = idPlanillaDistribucion;
    }

    public Integer getOrdenPlanilla() {
        return ordenPlanilla;
    }

    public void setOrdenPlanilla(Integer ordenPlanilla) {
        this.ordenPlanilla = ordenPlanilla;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof PlanillaDistribucionXMensajeriaDocumental)) {
            return false;
        }
        PlanillaDistribucionXMensajeriaDocumental other = (PlanillaDistribucionXMensajeriaDocumental) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.document.PlanillaDistribucionXMensajeriaDocumental[ id=" + id + " ]";
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }
    
}
