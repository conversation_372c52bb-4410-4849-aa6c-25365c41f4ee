package com.base16.gedsys.webservices;

import com.base16.gedsys.Enumeration.EstadosIncidenciaGeneralEnum;
import com.base16.gedsys.bean.BaseBean;
import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.attachments.Attachment;
import com.base16.gedsys.domain.model.attachments.TraceabilityAttachment;
import com.base16.gedsys.domain.model.document.Anexos;
import com.base16.gedsys.domain.model.document.Entidad;
import com.base16.gedsys.domain.model.location.Municipio;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.incidencias.IncidenciaService;
import com.base16.gedsys.incidencias.general.dto.IncidenciaGeneralMapper;
import com.base16.gedsys.incidencias.general.dto.IncidenciaGeneralVoucherDTO;
import com.base16.gedsys.infrastructure.persistence.jpa.AnexosJpaController;
import com.base16.gedsys.infrastructure.persistence.jpa.IncidenciaJpaRepository;
import com.base16.gedsys.infrastructure.persistence.jpa.MunicipioJpaRepository;
import com.base16.gedsys.infrastructure.persistence.jpa.TipoDocumentalJpaRepository;
import com.base16.utils.CanalesEntradaPQRSF;
import com.base16.utils.DateTimeUtils;
import com.base16.utils.KeyConfiguration;
import com.base16.utils.ServicioEditorWeb;
import com.base16.utils.WebEditor;
import com.base16.gedsys.configuracion.ConfiguracionServicesImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.ejb.TransactionManagement;
import javax.ejb.TransactionManagementType;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.Json;
import javax.json.JsonArrayBuilder;
import javax.json.JsonObject;
import javax.json.JsonObjectBuilder;
import javax.json.JsonValue;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import java.io.File;
import java.io.StringReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URI;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


@Path("/pqrsf")
@RequestScoped
@TransactionManagement(TransactionManagementType.BEAN)
public class GestionPQRSF extends BaseBean {
    @Inject
    private TipoDocumentalJpaRepository tipoDocumentalJpaRepository;
    @Inject
    private MunicipioJpaRepository municipioJpaRepository;
    @Inject
    private IncidenciaJpaRepository incidenciaJpaRepository;
    @Inject
    private IncidenciaService incidenciaService;
    @Inject
    private AnexosJpaController anexosJpaController;

    @POST
    @Path("/registros/v1")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response PostJson(String data) {
        IncidenciaGeneral incidencia = new IncidenciaGeneral();
        Entidad entidad;

        String token, radicado, tipoIncidencia;
        try {
            //Extracción entidad
            JsonObject content = Json
                    .createReader(new StringReader(data)).readObject();
            entidad = new Entidad();
            fillEntity(content, entidad);
            incidencia.setEntidad(entidad);

            //registro de anexos
            token = content.getString("anexos", "");
            if (!token.isEmpty()) {
                final Attachment attachment = new Attachment();
                attachment.setDescripcion("Archivo cargado por usuario Web");
                attachment.setRecursoRemoto(true);
                attachment.setTokenToDownload(token);
                incidencia.setAttachments(
                        new ArrayList<Attachment>() {
                            {
                                add(attachment);
                            }
                        }
                );
                //Trazabilidad Anexos
                TraceabilityAttachment traceabilityAttachment = new TraceabilityAttachment();
                traceabilityAttachment.setAttachment(attachment);
                traceabilityAttachment.setAccion("Recurso registrado vía Web, pendiente de descarga.");
                List<TraceabilityAttachment> traceabilityAttachmentList = new ArrayList<>();
                traceabilityAttachmentList.add(traceabilityAttachment);
                attachment.setTrazabilidadList(traceabilityAttachmentList);
            }

            //Reconocimiento tipo pqrsf
            tipoIncidencia = content.getString("proceso");
            TipoDocumental tipoDocumentalAsociado = tipoDocumentalJpaRepository
                    .findPQRSFbyChar(tipoIncidencia);
            incidencia.setTipoIncidencia(tipoDocumentalAsociado);

            //asignar estado
            incidencia.setEstado(EstadosIncidenciaGeneralEnum.RECIBIDA.getEstado());
            incidencia.setCanalEntrada(CanalesEntradaPQRSF.WEB.getCanal());

            incidencia.setAsunto(content.getString("asunto", ""));
            incidencia.setDescripcion(content.getString("descripcion", ""));

            incidenciaService.radicarIncidencia(incidencia);
            radicado = incidencia.getRadicado();
        } catch (NullPointerException np) {
            np.printStackTrace();
            return Response.status(Response.Status.BAD_REQUEST).build();
        } catch (Exception e) {
            e.printStackTrace();
            return Response.status(Response.Status.CONFLICT).build();
        } finally {
            String requestBody = String.format("REQUEST BODY: %s", data);
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, requestBody);
        }
        return Response.status(Response.Status.CREATED).entity(radicado).build();
    }

    @GET
    @Path("/consultas")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getJson(@QueryParam("radicado") String radicado,
                            @Context javax.servlet.http.HttpServletRequest request) {
        try {
            List<IncidenciaGeneral> incidencias = incidenciaJpaRepository.finByRadicado(radicado);
            if (!Optional.ofNullable(incidencias).isPresent() || incidencias.isEmpty())
                return Response.status(404).entity("{}").build();

            JsonObjectBuilder response = Json.createObjectBuilder();
            IncidenciaGeneral incidencia = incidencias.get(0);

            JsonObjectBuilder body = Json.createObjectBuilder()
                    .add("radicado", incidencia.getRadicado())
                    .add("asunto", incidencia.getAsunto())
                    .add("fechaRadicado", incidencia.getFechaCreacion().getTime());


            boolean answered = Optional.ofNullable(incidencia.getRespuesta()).isPresent()
                    && Optional.ofNullable(incidencia.getRespuesta().getRadicadoEnvio()).isPresent();

            if (answered) {
                String urlDocumentResponse = String.format("%s://%s:%s%s/webresources/Download/pdf?id=%s", request.getScheme(), request.getServerName()
                        , request.getServerPort(), request.getContextPath(), incidencia.getRespuesta().getId());
                body
                        .add("fechaRespuesta", incidencia.getFechaRespuesta().getTime())
                        .add("respuesta", urlDocumentResponse)
                        .add("radicadoEnvio", incidencia.getRespuesta().getRadicadoEnvio());

                // Include anexos with documentEnvio = true
                JsonArrayBuilder anexosArray = Json.createArrayBuilder();
                Optional.ofNullable(incidencia.getRespuesta().getAnexosList())
                        .ifPresent(anexosList -> {
                            anexosList.stream()
                                    .filter(Objects::nonNull)
                                    .filter(anexo -> Boolean.TRUE.equals(anexo.getDocumentEnvio()))
                                    .forEach(anexo -> {
                                        anexosArray.add(Json.createObjectBuilder()
                                                .add("id", anexo.getId() != null ? anexo.getId() : -1)
                                                .add("nombreAnexo", Optional.ofNullable(anexo.getNombreAnexo()).orElse(""))
                                                .add("rutaAnexo", Optional.ofNullable(anexo.getRutaAnexo()).orElse(""))
                                                .add("formato", Optional.ofNullable(anexo.getFormato()).orElse(""))
                                        );
                                    });
                        });
                body.add("anexos", anexosArray.build());
            }

            response.add(
                    IssueType.valueOf(incidencia.getTipoIncidencia().getTipoIncidencia()).getType(), body.build()
            );

            return Response.ok().entity(response.build().toString()).build();

        } catch (Exception e) {
            e.printStackTrace();
            return Response.status(409).build();//Conflict
        }
    }

    enum IssueType {
        p("Petición"),
        q("Queja"),
        r("Reclamo"),
        s("Solicitud"),
        f("Felicitación"),
        d("Denuncia");

        String type;

        IssueType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

    private void fillEntity(JsonObject json, Entidad entidad) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException, ParseException {

        HashMap<String, Method> methodHashMap = new HashMap<String, Method>() {
            {
                put("tratamiento", Entidad.class.getMethod("setTratamiento", String.class));
            }

            {
                put("cargo", Entidad.class.getMethod("setCargo", String.class));
            }

            {
                put("responsable", Entidad.class.getMethod("setContacto", String.class));
            }

            {
                put("direccion", Entidad.class.getMethod("setDireccion", String.class));
            }

            {
                put("email", Entidad.class.getMethod("setEmail", String.class));
            }

            {
                put("documento", Entidad.class.getMethod("setTipoDocumento", String.class));
            }

            {
                put("identificacion", Entidad.class.getMethod("setNumeroDocumento", String.class));
            }

            {
                put("genero", Entidad.class.getMethod("setGenero", String.class));
            }

            {
                put("telefono", Entidad.class.getMethod("setTelefono", String.class));
            }

            {
                put("nombre", Entidad.class.getMethod("setNombre", String.class));
            }
        };

        for (String key :
                json.keySet()) {
            if (methodHashMap.containsKey(key)) {
                Method method = methodHashMap.get(key);
                switch (json.get(key).getValueType()) {
                    case STRING:
                        method.invoke(entidad, json.getString(key));
                        break;
                    case NUMBER:
                        method.invoke(entidad, json.getInt(key));
                        break;
                    case NULL:
                        break;
                    default:
                        method.invoke(entidad, json.getBoolean(key));
                }
            }
        }

        final String birthdayKey = "birthday";
        final String cityKey = "ciudad";
        final String stateKey = "departamento";

        if (json.containsKey(birthdayKey)
                && !JsonValue.NULL.equals(json.get(birthdayKey))
                && !"0".equals(json.getString(birthdayKey))) {
            Date birthdate = new DateTimeUtils()
                    .getDateFromMillisecondsInUtc(Long.parseLong(json.getString(birthdayKey)));
            entidad.setBirthday(birthdate);
        }

        if (json.containsKey(cityKey)) {
            Municipio municipio = municipioJpaRepository
                    .buscarMunicipioPorCodigos(json.getString(cityKey), json.getString(stateKey));
            entidad.setMunicipio(municipio);
        }

        entidad.setTemporal(true);
    }

    @GET
    @Path("/voucher")
    @Produces({"application/pdf"})
    public Response downloadVoucher(@QueryParam("issue") Integer issueId,
                                    @Context HttpServletRequest request) {
        try {
            WebEditor localWebEditor = new WebEditor();
            WebEditor remoteWebEditor = new WebEditor(request);

            IncidenciaGeneral general = incidenciaJpaRepository.find(issueId);
            final IncidenciaGeneralMapper mapper = new IncidenciaGeneralMapper();
            IncidenciaGeneralVoucherDTO voucherDTO = mapper.IncidenciaGeneralToVoucherDTO(general);
            ObjectMapper jsonMapper = new ObjectMapper();
            String voucherJson = jsonMapper.writeValueAsString(voucherDTO);
            String voucherFilename = localWebEditor.getVoucherIDFromJson(voucherJson);
            if (voucherFilename.isEmpty()) {
                return Response.noContent().build();
            }
            URI urlToRedirect = new URI(remoteWebEditor.getServiceURLForGetMethodRequests(ServicioEditorWeb.PDFVIEWER) + voucherFilename);
            return Response.temporaryRedirect(urlToRedirect).build();
        } catch (Exception ex) {
            Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
            return Response.serverError().build();
        }
    }

    @GET
    @Path("/anexos/{id}")
    @Produces("*/*")
    public Response downloadAnexo(@PathParam("id") Integer id) {
        try {
            Anexos anexo = anexosJpaController.find(id);

            Response validationError = validateAnexo(anexo, id);
            if (validationError != null) return validationError;

            String basePath = getBasePath();
            if (basePath == null) {
                return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
            }

            java.nio.file.Path filePath = buildAndValidateFilePath(basePath, anexo, id);
            if (filePath == null) {
                return Response.status(Response.Status.FORBIDDEN).build();
            }

            if (!java.nio.file.Files.exists(filePath)) {
                Logger.getLogger(GestionPQRSF.class.getName()).log(Level.WARNING,
                    "File not found at path: {0}", filePath);
                return Response.status(Response.Status.NOT_FOUND).build();
            }

            String contentType = determineContentType(filePath, anexo.getFormato());
            String downloadFilename = buildDownloadFilename(anexo, id);

            StreamingOutput stream = output -> java.nio.file.Files.copy(filePath, output);

            return Response.ok(stream)
                .type(contentType)
                .header("Content-Disposition", "attachment; filename=\"" + downloadFilename + "\"")
                .build();

        } catch (Exception ex) {
            Logger.getLogger(GestionPQRSF.class.getName()).log(Level.SEVERE, ex,
                () -> "Error downloading anexo with id: " + id);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
        }
    }

    private Response validateAnexo(Anexos anexo, Integer id) {
        if (anexo == null) {
            Logger.getLogger(GestionPQRSF.class.getName()).log(Level.WARNING,
                "Anexo not found with id: {0}", id);
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        if (!Boolean.TRUE.equals(anexo.getDocumentEnvio())) {
            Logger.getLogger(GestionPQRSF.class.getName()).log(Level.WARNING,
                "Attempted to download anexo with documentEnvio=false, id: {0}", id);
            return Response.status(Response.Status.FORBIDDEN).build();
        }

        if (anexo.getRutaAnexo() == null || anexo.getRutaAnexo().isEmpty() ||
            anexo.getFormato() == null || anexo.getFormato().isEmpty()) {
            Logger.getLogger(GestionPQRSF.class.getName()).log(Level.WARNING,
                "Anexo has missing or empty rutaAnexo or formato, id: {0}", id);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
        }

        return null;
    }

    private String getBasePath() {
        String basePath = ConfiguracionServicesImpl.consultarValorConfigGeneral(
            KeyConfiguration.keyPathData.getKeyConfiguration());

        if (basePath == null || basePath.isEmpty()) {
            Logger.getLogger(GestionPQRSF.class.getName()).log(Level.SEVERE,
                "Base path configuration is missing");
            return null;
        }

        return basePath;
    }

    private java.nio.file.Path buildAndValidateFilePath(String basePath, Anexos anexo, Integer id) {
        java.nio.file.Path baseDir = java.nio.file.Paths.get(basePath, "Anexos", "RadicacionEnvio")
            .normalize().toAbsolutePath();
        String fileName = anexo.getRutaAnexo() + "." + anexo.getFormato();
        java.nio.file.Path filePath = java.nio.file.Paths.get(basePath, "Anexos", "RadicacionEnvio", fileName)
            .normalize().toAbsolutePath();

        if (!filePath.startsWith(baseDir)) {
            Logger.getLogger(GestionPQRSF.class.getName()).log(Level.WARNING,
                "Path traversal attempt detected for anexo id: {0}", id);
            return null;
        }

        return filePath;
    }

    private String determineContentType(java.nio.file.Path filePath, String formato) {
        String contentType = null;
        try {
            contentType = java.nio.file.Files.probeContentType(filePath);
        } catch (Exception ignore) {
            // fallback below
        }

        if (contentType == null) {
            String formatoLower = formato.toLowerCase(Locale.ROOT);
            switch (formatoLower) {
                case "jpg":
                case "jpeg":
                    contentType = "image/jpeg"; break;
                case "png":
                    contentType = "image/png"; break;
                case "gif":
                    contentType = "image/gif"; break;
                case "pdf":
                    contentType = "application/pdf"; break;
                case "mp3":
                    contentType = "audio/mpeg"; break;
                case "ogg":
                    contentType = "audio/ogg"; break;
                case "mp4":
                    contentType = "video/mp4"; break;
                case "avi":
                    contentType = "video/x-msvideo"; break;
                default:
                    contentType = "application/octet-stream";
            }
        }

        return contentType;
    }

    private String buildDownloadFilename(Anexos anexo, Integer id) {
        String nombreArchivo = anexo.getNombreAnexo() != null ? anexo.getNombreAnexo() : "anexo_" + id;
        String downloadFilename = nombreArchivo + "." + anexo.getFormato();
        return downloadFilename.replaceAll("[\r\n\"]", "_");
    }

}
